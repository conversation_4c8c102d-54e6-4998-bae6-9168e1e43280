"""
Bloom Telegram Bot for Solana Trading Bot integration.
Provides real-time notifications and trading functionality through Telegram.
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler, MessageHandler,
    filters, ContextTypes
)

from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import format_sol_amount, format_percentage, get_current_datetime


class BloomBot(LoggerMixin):
    """Bloom Telegram Bot for trading notifications and controls."""
    
    def __init__(self, token: str):
        super().__init__()
        self.token = token
        self.application = None
        self.authorized_users = set()
        self.notification_callbacks = []
        self.is_running = False
        
        # Trading state
        self.trading_enabled = True
        self.notifications_enabled = True
        
        # Subscription management
        self.subscribers = {
            'all': set(),
            'trades': set(),
            'snipers': set(),
            'pump_launches': set(),
            'alerts': set()
        }
    
    async def initialize(self):
        """Initialize the Telegram bot."""
        try:
            self.application = Application.builder().token(self.token).build()
            
            # Register command handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("help", self.help_command))
            self.application.add_handler(CommandHandler("status", self.status_command))
            self.application.add_handler(CommandHandler("subscribe", self.subscribe_command))
            self.application.add_handler(CommandHandler("unsubscribe", self.unsubscribe_command))
            self.application.add_handler(CommandHandler("wallets", self.wallets_command))
            self.application.add_handler(CommandHandler("trades", self.trades_command))
            self.application.add_handler(CommandHandler("pump", self.pump_command))
            self.application.add_handler(CommandHandler("settings", self.settings_command))
            self.application.add_handler(CommandHandler("emergency", self.emergency_command))
            
            # Register callback query handler for inline keyboards
            self.application.add_handler(CallbackQueryHandler(self.button_callback))
            
            # Register message handler for text messages
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
            
            # Set bot commands
            await self.set_bot_commands()
            
            self.logger.info("Bloom Telegram bot initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Telegram bot: {e}")
            raise
    
    async def start(self):
        """Start the Telegram bot."""
        try:
            if self.is_running:
                self.logger.warning("Telegram bot is already running")
                return
            
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            
            self.is_running = True
            self.logger.info("Bloom Telegram bot started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start Telegram bot: {e}")
            raise
    
    async def stop(self):
        """Stop the Telegram bot."""
        try:
            if not self.is_running:
                return
            
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()
            
            self.is_running = False
            self.logger.info("Bloom Telegram bot stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping Telegram bot: {e}")
    
    async def set_bot_commands(self):
        """Set bot commands for the Telegram menu."""
        commands = [
            BotCommand("start", "Start the bot and get welcome message"),
            BotCommand("help", "Show help and available commands"),
            BotCommand("status", "Show bot status and performance"),
            BotCommand("subscribe", "Subscribe to notifications"),
            BotCommand("unsubscribe", "Unsubscribe from notifications"),
            BotCommand("wallets", "Show tracked wallets"),
            BotCommand("trades", "Show recent trades"),
            BotCommand("pump", "Show pump.fun statistics"),
            BotCommand("settings", "Bot settings and controls"),
            BotCommand("emergency", "Emergency stop all trading"),
        ]
        
        await self.application.bot.set_my_commands(commands)
    
    def add_authorized_user(self, user_id: int):
        """Add an authorized user."""
        self.authorized_users.add(user_id)
        self.logger.info(f"Added authorized user: {user_id}")
    
    def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized."""
        return user_id in self.authorized_users
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        user_id = update.effective_user.id
        
        if not self.is_authorized(user_id):
            await update.message.reply_text(
                "🚫 Unauthorized access. Contact the bot administrator."
            )
            return
        
        welcome_text = """
🤖 **Bloom Solana Trading Bot**

Welcome to your personal Solana trading assistant! I monitor pump.fun and track profitable traders 24/7.

🎯 **What I do:**
• Track snipers, insiders, devs & whales
• Monitor pump.fun launches in real-time
• Copy trade profitable wallets
• Send instant notifications

📱 **Quick Commands:**
/status - Bot performance
/subscribe - Get notifications
/wallets - Top traders
/pump - Pump.fun stats
/settings - Bot controls

Ready to start making profits! 🚀
        """
        
        keyboard = [
            [InlineKeyboardButton("📊 Status", callback_data="status"),
             InlineKeyboardButton("🔔 Subscribe", callback_data="subscribe")],
            [InlineKeyboardButton("👥 Wallets", callback_data="wallets"),
             InlineKeyboardButton("🚀 Pump.fun", callback_data="pump")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(welcome_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        if not self.is_authorized(update.effective_user.id):
            return
        
        help_text = """
🤖 **Bloom Bot Commands**

**📊 Monitoring:**
/status - Bot status and performance
/wallets - Show tracked profitable wallets
/trades - Recent copy trades
/pump - Pump.fun statistics and launches

**🔔 Notifications:**
/subscribe [type] - Subscribe to notifications
  • all - All notifications
  • trades - Copy trade alerts
  • snipers - New sniper discoveries
  • pump_launches - New pump.fun tokens
  • alerts - Important alerts only

/unsubscribe [type] - Unsubscribe from notifications

**⚙️ Controls:**
/settings - Bot settings and controls
/emergency - Emergency stop all trading

**💡 Tips:**
• Use inline buttons for quick actions
• Subscribe to get real-time alerts
• Check /pump for latest launches
• Monitor /status for performance
        """
        
        await update.message.reply_text(help_text, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        if not self.is_authorized(update.effective_user.id):
            return
        
        try:
            # Get bot status (this would integrate with the main bot)
            status_text = await self.get_status_message()
            
            keyboard = [
                [InlineKeyboardButton("🔄 Refresh", callback_data="status"),
                 InlineKeyboardButton("⚙️ Settings", callback_data="settings")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(status_text, reply_markup=reply_markup, parse_mode='Markdown')
            
        except Exception as e:
            await update.message.reply_text(f"❌ Error getting status: {e}")
    
    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /subscribe command."""
        if not self.is_authorized(update.effective_user.id):
            return
        
        user_id = update.effective_user.id
        args = context.args
        
        if not args:
            # Show subscription options
            keyboard = [
                [InlineKeyboardButton("🔔 All Notifications", callback_data="sub_all"),
                 InlineKeyboardButton("💰 Trades Only", callback_data="sub_trades")],
                [InlineKeyboardButton("🎯 Snipers", callback_data="sub_snipers"),
                 InlineKeyboardButton("🚀 Pump Launches", callback_data="sub_pump_launches")],
                [InlineKeyboardButton("⚠️ Alerts Only", callback_data="sub_alerts")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                "🔔 **Choose Notification Type:**",
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            return
        
        subscription_type = args[0].lower()
        if subscription_type in self.subscribers:
            self.subscribers[subscription_type].add(user_id)
            await update.message.reply_text(f"✅ Subscribed to {subscription_type} notifications!")
        else:
            await update.message.reply_text("❌ Invalid subscription type. Use: all, trades, snipers, pump_launches, alerts")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard button callbacks."""
        query = update.callback_query
        await query.answer()
        
        if not self.is_authorized(query.from_user.id):
            return
        
        data = query.data
        
        if data == "status":
            status_text = await self.get_status_message()
            await query.edit_message_text(status_text, parse_mode='Markdown')
        
        elif data.startswith("sub_"):
            subscription_type = data[4:]  # Remove "sub_" prefix
            user_id = query.from_user.id
            self.subscribers[subscription_type].add(user_id)
            await query.edit_message_text(f"✅ Subscribed to {subscription_type} notifications!")
        
        elif data == "emergency_stop":
            await self.handle_emergency_stop(query)
        
        elif data == "toggle_trading":
            self.trading_enabled = not self.trading_enabled
            status = "enabled" if self.trading_enabled else "disabled"
            await query.edit_message_text(f"🔄 Trading {status}")
    
    async def get_status_message(self) -> str:
        """Generate status message."""
        try:
            # This would integrate with the main bot to get real status
            # For now, return a template
            
            current_time = get_current_datetime().strftime("%H:%M:%S UTC")
            
            status_text = f"""
📊 **Bloom Bot Status** - {current_time}

🤖 **Bot Status:**
• Status: {'🟢 Running' if self.is_running else '🔴 Stopped'}
• Trading: {'🟢 Enabled' if self.trading_enabled else '🔴 Disabled'}
• Notifications: {'🟢 On' if self.notifications_enabled else '🔴 Off'}

💰 **Performance (24h):**
• Copy Trades: 12
• Success Rate: 75%
• Total P&L: +2.45 SOL
• Active Positions: 3

🎯 **Tracking:**
• Monitored Wallets: 45
• Snipers Found: 8
• Pump Launches: 23
• Subscribers: {len(self.subscribers['all'])}

🚀 **Pump.fun:**
• New Tokens (1h): 5
• Snipes Detected: 12
• Top ROI: 15.6x
            """
            
            return status_text
            
        except Exception as e:
            return f"❌ Error getting status: {e}"
    
    async def handle_emergency_stop(self, query):
        """Handle emergency stop."""
        try:
            # This would integrate with the main bot's emergency stop
            self.trading_enabled = False
            
            await query.edit_message_text(
                "🚨 **EMERGENCY STOP ACTIVATED**\n\n"
                "• All trading halted\n"
                "• Positions being closed\n"
                "• Risk management active\n\n"
                "Use /settings to re-enable trading."
            )
            
            # Notify all subscribers
            await self.broadcast_message(
                "🚨 **EMERGENCY STOP**\nAll trading has been halted by user command.",
                'alerts'
            )
            
        except Exception as e:
            await query.edit_message_text(f"❌ Error during emergency stop: {e}")
    
    async def broadcast_message(self, message: str, subscription_type: str = 'all'):
        """Broadcast message to subscribers."""
        try:
            subscribers = self.subscribers.get(subscription_type, set())
            
            for user_id in subscribers:
                try:
                    await self.application.bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    self.logger.error(f"Failed to send message to {user_id}: {e}")
            
        except Exception as e:
            self.logger.error(f"Error broadcasting message: {e}")
    
    async def send_trade_notification(self, trade_data: Dict[str, Any]):
        """Send trade notification to subscribers."""
        try:
            message = f"""
💰 **Copy Trade Executed**

🎯 **Source:** {trade_data.get('source_wallet', 'Unknown')[:8]}...
🪙 **Token:** {trade_data.get('token_symbol', 'Unknown')}
📊 **Type:** {trade_data.get('trade_type', 'Unknown').upper()}
💵 **Amount:** {format_sol_amount(trade_data.get('amount_sol', 0))}
⚡ **Speed:** {trade_data.get('delay_seconds', 0):.1f}s delay

🔗 **Signature:** `{trade_data.get('signature', 'Unknown')[:16]}...`
            """
            
            await self.broadcast_message(message, 'trades')
            
        except Exception as e:
            self.logger.error(f"Error sending trade notification: {e}")
    
    async def send_sniper_notification(self, sniper_data: Dict[str, Any]):
        """Send sniper discovery notification."""
        try:
            message = f"""
🎯 **New Sniper Discovered**

👤 **Wallet:** {sniper_data.get('address', 'Unknown')[:8]}...
📈 **Success Rate:** {format_percentage(sniper_data.get('success_rate', 0))}
⚡ **Avg Speed:** {sniper_data.get('avg_speed', 0):.1f}s
💎 **Best ROI:** {sniper_data.get('best_roi', 0):.1f}x
🔄 **Copy Trading:** {'✅ Enabled' if sniper_data.get('copy_enabled') else '❌ Disabled'}

This wallet has been added to our tracking list!
            """
            
            await self.broadcast_message(message, 'snipers')
            
        except Exception as e:
            self.logger.error(f"Error sending sniper notification: {e}")
    
    async def send_pump_launch_notification(self, token_data: Dict[str, Any]):
        """Send pump.fun launch notification."""
        try:
            message = f"""
🚀 **New Pump.fun Launch**

🪙 **Token:** {token_data.get('symbol', 'Unknown')} ({token_data.get('name', 'Unknown')})
👤 **Creator:** {token_data.get('creator', 'Unknown')[:8]}...
💰 **Market Cap:** {format_sol_amount(token_data.get('market_cap', 0))}
⏰ **Launched:** {token_data.get('launch_time', 'Unknown')}

🔗 **Mint:** `{token_data.get('mint', 'Unknown')[:16]}...`

Monitoring for snipe opportunities...
            """
            
            await self.broadcast_message(message, 'pump_launches')
            
        except Exception as e:
            self.logger.error(f"Error sending pump launch notification: {e}")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages."""
        if not self.is_authorized(update.effective_user.id):
            return
        
        text = update.message.text.lower()
        
        if "status" in text:
            await self.status_command(update, context)
        elif "help" in text:
            await self.help_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 Use /help to see available commands or use the menu buttons!"
            )


# Global Bloom bot instance (will be initialized with token)
bloom_bot = None
