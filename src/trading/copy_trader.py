"""
Copy trading engine for the Solana Trading Bot.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.blockchain.transaction_monitor import SwapTransaction
from src.blockchain.solana_client import solana_client
from src.data.database import db_manager, CopyTradeRepository, WalletRepository
from src.trading.risk_manager import risk_manager
from src.trading.order_executor import order_executor
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import calculate_position_size, get_current_datetime


@dataclass
class CopyTradeSignal:
    """Represents a copy trade signal."""

    source_wallet: str
    source_signature: str
    token_mint: str
    trade_type: str  # buy, sell
    source_amount_sol: float
    source_timestamp: datetime
    confidence_multiplier: float
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None


class CopyTrader(LoggerMixin):
    """Handles copy trading operations."""

    def __init__(self):
        super().__init__()
        self.copy_trade_repo = CopyTradeRepository(db_manager)
        self.wallet_repo = WalletRepository(db_manager)

        # Active copy trades tracking
        self.active_positions = {}
        self.pending_orders = {}

        # Configuration
        self.config = settings.trading.copy_trading
        self.risk_config = settings.trading.risk_management

    async def process_swap_for_copy_trading(self, swap: SwapTransaction):
        """Process a swap transaction to determine if it should be copy traded."""
        try:
            # Check if this wallet is being copy traded
            wallet = await self.wallet_repo.get_wallet_by_address(swap.wallet_address)

            if not wallet or not wallet.is_copy_trading:
                return

            # Check if copy trading is enabled
            if not self.config.enabled or not settings.features.enable_copy_trading:
                return

            # Create copy trade signal
            signal = await self._create_copy_trade_signal(swap, wallet)

            if signal:
                # Execute copy trade
                await self._execute_copy_trade(signal)

        except Exception as e:
            self.logger.error(f"Error processing swap for copy trading: {e}")

    async def _create_copy_trade_signal(
        self, swap: SwapTransaction, wallet
    ) -> Optional[CopyTradeSignal]:
        """Create a copy trade signal from a swap transaction."""
        try:
            # Calculate confidence multiplier based on wallet performance
            confidence_multiplier = self._calculate_confidence_multiplier(wallet)

            # Determine the token to trade
            token_mint = swap.token_out if swap.is_buy else swap.token_in

            # Skip if trading SOL directly
            if token_mint == "So11111111111111111111111111111111111111112":
                return None

            # Calculate stop loss and take profit prices
            stop_loss_price = None
            take_profit_price = None

            if swap.is_buy:
                # For buy orders, set stop loss and take profit
                entry_price = (
                    swap.amount_in / swap.amount_out if swap.amount_out > 0 else 0
                )
                if entry_price > 0:
                    stop_loss_price = entry_price * (
                        1 - self.risk_config.stop_loss_percentage
                    )
                    take_profit_price = entry_price * (
                        1 + self.risk_config.take_profit_percentage
                    )

            return CopyTradeSignal(
                source_wallet=swap.wallet_address,
                source_signature=swap.signature,
                token_mint=token_mint,
                trade_type="buy" if swap.is_buy else "sell",
                source_amount_sol=(
                    swap.amount_in
                    if swap.token_in == "So11111111111111111111111111111111111111112"
                    else swap.amount_out
                ),
                source_timestamp=swap.timestamp,
                confidence_multiplier=confidence_multiplier,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
            )

        except Exception as e:
            self.logger.error(f"Error creating copy trade signal: {e}")
            return None

    def _calculate_confidence_multiplier(self, wallet) -> float:
        """Calculate position size multiplier based on wallet confidence."""
        try:
            confidence_score = wallet.confidence_score or 0.5

            # Map confidence to multiplier
            if confidence_score >= 0.9:
                return settings.trading.position_sizing.size_multiplier_by_confidence[
                    "high"
                ]
            elif confidence_score >= 0.7:
                return settings.trading.position_sizing.size_multiplier_by_confidence[
                    "medium"
                ]
            else:
                return settings.trading.position_sizing.size_multiplier_by_confidence[
                    "low"
                ]

        except Exception as e:
            self.logger.error(f"Error calculating confidence multiplier: {e}")
            return 1.0

    async def _execute_copy_trade(self, signal: CopyTradeSignal):
        """Execute a copy trade based on the signal."""
        try:
            # Check risk management constraints
            if not await risk_manager.can_execute_trade(signal):
                self.logger.info(
                    f"Trade blocked by risk management: {signal.token_mint}"
                )
                return

            # Calculate position size
            position_size = self._calculate_copy_trade_position_size(signal)

            if position_size < self.config.min_position_size_sol:
                self.logger.info(f"Position size too small: {position_size} SOL")
                return

            # Create copy trade record
            copy_trade = await self.copy_trade_repo.create_copy_trade(
                source_wallet_id=(
                    await self.wallet_repo.get_wallet_by_address(signal.source_wallet)
                ).id,
                source_signature=signal.source_signature,
                token_mint=signal.token_mint,
                trade_type=signal.trade_type,
                intended_amount_sol=position_size,
                source_time=signal.source_timestamp,
                stop_loss_price=signal.stop_loss_price,
                take_profit_price=signal.take_profit_price,
                position_size_multiplier=signal.confidence_multiplier,
                status="pending",
            )

            # Execute the trade
            execution_result = await order_executor.execute_swap(
                token_mint=signal.token_mint,
                trade_type=signal.trade_type,
                amount_sol=position_size,
                slippage_tolerance=self.config.slippage_tolerance,
            )

            # Update copy trade with execution results
            if execution_result.success:
                await self.copy_trade_repo.update_copy_trade_status(
                    copy_trade.id,
                    "executed",
                    our_signature=execution_result.signature,
                    actual_amount_sol=execution_result.actual_amount_sol,
                    token_amount=execution_result.token_amount,
                    execution_price=execution_result.execution_price,
                    execution_time=get_current_datetime(),
                    delay_seconds=(
                        get_current_datetime() - signal.source_timestamp
                    ).total_seconds(),
                )

                # Track active position for risk management
                if signal.trade_type == "buy":
                    self.active_positions[signal.token_mint] = {
                        "copy_trade_id": copy_trade.id,
                        "entry_price": execution_result.execution_price,
                        "amount": execution_result.token_amount,
                        "stop_loss": signal.stop_loss_price,
                        "take_profit": signal.take_profit_price,
                        "entry_time": get_current_datetime(),
                    }
                elif (
                    signal.trade_type == "sell"
                    and signal.token_mint in self.active_positions
                ):
                    # Remove from active positions
                    del self.active_positions[signal.token_mint]

                self.log_trade_event(
                    "copy_trade_executed",
                    {
                        "source_wallet": signal.source_wallet,
                        "token_mint": signal.token_mint,
                        "trade_type": signal.trade_type,
                        "amount_sol": position_size,
                        "execution_price": execution_result.execution_price,
                        "signature": execution_result.signature,
                    },
                )

                # Send Telegram notification
                try:
                    from src.telegram.notification_manager import notification_manager

                    await notification_manager.notify_trade_executed(
                        {
                            "source_wallet": signal.source_wallet,
                            "token_symbol": signal.token_mint[:8]
                            + "...",  # Shortened for display
                            "trade_type": signal.trade_type,
                            "amount_sol": position_size,
                            "price": execution_result.execution_price,
                            "delay_seconds": (
                                get_current_datetime() - signal.source_timestamp
                            ).total_seconds(),
                            "signature": execution_result.signature,
                        }
                    )
                except Exception as e:
                    self.logger.error(f"Error sending trade notification: {e}")

            else:
                await self.copy_trade_repo.update_copy_trade_status(
                    copy_trade.id,
                    "failed",
                    error_message=execution_result.error_message,
                    execution_time=get_current_datetime(),
                    delay_seconds=(
                        get_current_datetime() - signal.source_timestamp
                    ).total_seconds(),
                )

                self.logger.error(
                    f"Copy trade execution failed: {execution_result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error executing copy trade: {e}")

    def _calculate_copy_trade_position_size(self, signal: CopyTradeSignal) -> float:
        """Calculate the position size for a copy trade."""
        try:
            # Get available balance (this would need to be implemented)
            available_balance = 10.0  # Placeholder - should get actual SOL balance

            # Calculate base position size
            base_size = settings.trading.position_sizing.base_size_sol

            # Apply confidence multiplier
            target_size = calculate_position_size(
                base_size=base_size,
                confidence_multiplier=signal.confidence_multiplier,
                max_size=self.config.max_position_size_sol,
                available_balance=available_balance,
            )

            return target_size

        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0.0

    async def monitor_active_positions(self):
        """Monitor active positions for stop loss and take profit."""
        try:
            for token_mint, position in list(self.active_positions.items()):
                try:
                    # Get current token price (this would need to be implemented)
                    current_price = await self._get_current_token_price(token_mint)

                    if not current_price:
                        continue

                    # Check stop loss
                    if position["stop_loss"] and current_price <= position["stop_loss"]:
                        await self._execute_stop_loss(token_mint, position)
                        continue

                    # Check take profit
                    if (
                        position["take_profit"]
                        and current_price >= position["take_profit"]
                    ):
                        await self._execute_take_profit(token_mint, position)
                        continue

                except Exception as e:
                    self.logger.error(f"Error monitoring position {token_mint}: {e}")

        except Exception as e:
            self.logger.error(f"Error monitoring active positions: {e}")

    async def _get_current_token_price(self, token_mint: str) -> Optional[float]:
        """Get current token price in SOL."""
        try:
            # This would integrate with Jupiter API or other price sources
            # Placeholder implementation
            return None

        except Exception as e:
            self.logger.error(f"Error getting token price for {token_mint}: {e}")
            return None

    async def _execute_stop_loss(self, token_mint: str, position: Dict[str, Any]):
        """Execute stop loss for a position."""
        try:
            self.logger.info(f"Executing stop loss for {token_mint}")

            # Execute sell order
            execution_result = await order_executor.execute_swap(
                token_mint=token_mint,
                trade_type="sell",
                amount_sol=None,  # Sell all tokens
                slippage_tolerance=self.config.slippage_tolerance
                * 2,  # Higher slippage for stop loss
            )

            if execution_result.success:
                # Remove from active positions
                del self.active_positions[token_mint]

                self.log_trade_event(
                    "stop_loss_executed",
                    {
                        "token_mint": token_mint,
                        "exit_price": execution_result.execution_price,
                        "signature": execution_result.signature,
                    },
                )

        except Exception as e:
            self.logger.error(f"Error executing stop loss for {token_mint}: {e}")

    async def _execute_take_profit(self, token_mint: str, position: Dict[str, Any]):
        """Execute take profit for a position."""
        try:
            self.logger.info(f"Executing take profit for {token_mint}")

            # Execute sell order
            execution_result = await order_executor.execute_swap(
                token_mint=token_mint,
                trade_type="sell",
                amount_sol=None,  # Sell all tokens
                slippage_tolerance=self.config.slippage_tolerance,
            )

            if execution_result.success:
                # Remove from active positions
                del self.active_positions[token_mint]

                self.log_trade_event(
                    "take_profit_executed",
                    {
                        "token_mint": token_mint,
                        "exit_price": execution_result.execution_price,
                        "signature": execution_result.signature,
                    },
                )

        except Exception as e:
            self.logger.error(f"Error executing take profit for {token_mint}: {e}")

    async def get_copy_trade_performance(self) -> Dict[str, Any]:
        """Get copy trading performance metrics."""
        try:
            # Get all copy trades from database
            # This would need to be implemented in the repository

            return {
                "total_copy_trades": 0,
                "successful_trades": 0,
                "total_pnl_sol": 0.0,
                "win_rate": 0.0,
                "active_positions": len(self.active_positions),
                "avg_execution_delay_seconds": 0.0,
            }

        except Exception as e:
            self.logger.error(f"Error getting copy trade performance: {e}")
            return {}


# Global copy trader instance
copy_trader = CopyTrader()
