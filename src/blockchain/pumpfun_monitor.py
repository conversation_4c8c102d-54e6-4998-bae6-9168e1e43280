"""
Pump.fun specific monitoring for new token launches and early trading activity.
"""

import asyncio
import json
import aiohttp
import websockets
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.blockchain.solana_client import solana_client
from src.blockchain.transaction_monitor import SwapTransaction
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import get_current_datetime, is_valid_solana_address


@dataclass
class PumpFunToken:
    """Represents a pump.fun token."""

    mint: str
    name: str
    symbol: str
    description: str
    creator: str
    creation_time: datetime
    market_cap: float
    price_sol: float
    volume_24h: float
    holder_count: int
    is_graduated: bool
    bonding_curve: str
    metadata_uri: str


@dataclass
class PumpFunTrade:
    """Represents a pump.fun trade."""

    signature: str
    mint: str
    trader: str
    is_buy: bool
    sol_amount: float
    token_amount: float
    price_per_token: float
    market_cap_sol: float
    timestamp: datetime
    is_creation_trade: bool = False


class PumpFunMonitor(LoggerMixin):
    """Monitors pump.fun for new token launches and trading activity."""

    def __init__(self):
        super().__init__()
        self.is_monitoring = False

        # PumpPortal WebSocket API (from config.yaml)
        self.PUMPPORTAL_WS_URL = "wss://pumpportal.fun/api/data"
        self.PUMP_FUN_PROGRAM = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

        # WebSocket connection
        self.websocket = None
        self.websocket_task = None

        # Token tracking
        self.tracked_tokens = {}  # mint -> PumpFunToken
        self.recent_launches = []  # Recent token launches
        self.token_callbacks = []
        self.trade_callbacks = []

        # Performance tracking
        self.launch_detection_times = {}  # mint -> detection_time

        # Statistics
        self.tokens_detected = 0
        self.trades_detected = 0

    async def start_monitoring(self):
        """Start monitoring pump.fun via PumpPortal WebSocket."""
        if self.is_monitoring:
            self.logger.warning("Pump.fun monitoring is already running")
            return

        try:
            self.is_monitoring = True
            self.logger.info(
                "Starting pump.fun monitoring with PumpPortal WebSocket..."
            )

            # Start monitoring tasks
            tasks = [
                asyncio.create_task(self._monitor_pumpportal_websocket()),
                asyncio.create_task(self._cleanup_old_data()),
                asyncio.create_task(self._log_statistics()),
            ]

            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            self.logger.error(f"Error in pump.fun monitoring: {e}")
            raise

    async def stop_monitoring(self):
        """Stop monitoring pump.fun."""
        self.is_monitoring = False

        # Close WebSocket connection
        if self.websocket:
            try:
                await self.websocket.close()
            except:
                pass
            self.websocket = None

        if self.websocket_task:
            self.websocket_task.cancel()
            self.websocket_task = None

        self.logger.info("Pump.fun monitoring stopped")

    def add_token_callback(self, callback):
        """Add callback for new token launches."""
        self.token_callbacks.append(callback)

    def add_trade_callback(self, callback):
        """Add callback for pump.fun trades."""
        self.trade_callbacks.append(callback)

    async def _monitor_pumpportal_websocket(self):
        """Monitor PumpPortal WebSocket for real-time token launches and trades."""
        while self.is_monitoring:
            try:
                self.logger.info(
                    f"Connecting to PumpPortal WebSocket: {self.PUMPPORTAL_WS_URL}"
                )

                async with websockets.connect(
                    self.PUMPPORTAL_WS_URL,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10,
                ) as websocket:
                    self.websocket = websocket
                    self.logger.info("✅ Connected to PumpPortal WebSocket")

                    # Subscribe to new token launches
                    await self._subscribe_to_new_tokens(websocket)

                    # Listen for messages
                    async for message in websocket:
                        try:
                            await self._process_websocket_message(message)
                        except Exception as e:
                            self.logger.error(
                                f"Error processing WebSocket message: {e}"
                            )

            except websockets.exceptions.ConnectionClosed:
                self.logger.warning(
                    "PumpPortal WebSocket connection closed, reconnecting..."
                )
                await asyncio.sleep(5)
            except Exception as e:
                self.logger.error(f"PumpPortal WebSocket error: {e}")
                await asyncio.sleep(10)

    async def _subscribe_to_new_tokens(self, websocket):
        """Subscribe to new token launches on PumpPortal."""
        try:
            # Subscribe to new token creation events
            subscription = {"method": "subscribeNewToken"}
            await websocket.send(json.dumps(subscription))

            # Wait for confirmation
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            response_data = json.loads(response)

            if "message" in response_data:
                self.logger.info(
                    f"✅ Subscription successful: {response_data['message']}"
                )
            elif "errors" in response_data:
                self.logger.error(f"❌ Subscription failed: {response_data['errors']}")
            else:
                self.logger.info(f"📡 Subscription response: {response_data}")

        except Exception as e:
            self.logger.error(f"Error subscribing to new tokens: {e}")

    async def _process_websocket_message(self, message):
        """Process incoming WebSocket message from PumpPortal."""
        try:
            data = json.loads(message)

            # Check if this is a new token launch
            if self._is_new_token_message(data):
                await self._process_pumpportal_token(data)
                self.tokens_detected += 1

            # Check if this is a trade message
            elif self._is_trade_message(data):
                await self._process_pumpportal_trade(data)
                self.trades_detected += 1

        except json.JSONDecodeError:
            self.logger.warning("Received non-JSON message from PumpPortal")
        except Exception as e:
            self.logger.error(f"Error processing WebSocket message: {e}")

    def _is_new_token_message(self, data: Dict[str, Any]) -> bool:
        """Check if message represents a new token launch."""
        return (
            isinstance(data, dict)
            and "mint" in data
            and "name" in data
            and "symbol" in data
            and "initialBuy" in data
            and data.get("initialBuy") is True
        )

    def _is_trade_message(self, data: Dict[str, Any]) -> bool:
        """Check if message represents a trade."""
        return (
            isinstance(data, dict)
            and "signature" in data
            and "mint" in data
            and "traderPublicKey" in data
            and "solAmount" in data
        )

    async def _process_pumpportal_token(self, data: Dict[str, Any]):
        """Process a new token from PumpPortal WebSocket."""
        try:
            mint = data.get("mint")
            if not mint or mint in self.tracked_tokens:
                return

            # Create PumpFunToken object from PumpPortal data
            token = PumpFunToken(
                mint=mint,
                name=data.get("name", ""),
                symbol=data.get("symbol", ""),
                description="",  # Not provided by PumpPortal
                creator=data.get("traderPublicKey", ""),
                creation_time=get_current_datetime(),  # Use current time as creation time
                market_cap=float(data.get("marketCapSol", 0)),
                price_sol=0.0,  # Calculate from trade data
                volume_24h=0.0,  # Will be updated from trades
                holder_count=1,  # Start with 1 (creator)
                is_graduated=False,  # New tokens are not graduated
                bonding_curve=data.get("bondingCurveKey", ""),
                metadata_uri=data.get("uri", ""),
            )

            # Track the token
            self.tracked_tokens[mint] = token
            self.recent_launches.append(token)
            self.launch_detection_times[mint] = get_current_datetime()

            # Keep only recent launches (last 24 hours)
            cutoff_time = get_current_datetime() - timedelta(hours=24)
            self.recent_launches = [
                t for t in self.recent_launches if t.creation_time >= cutoff_time
            ]

            self.logger.info(f"🪙 New token detected: {token.symbol} ({mint[:8]}...)")

            # Send Telegram notification
            await self._send_new_token_notification(token)

            # Notify callbacks
            await self._notify_token_callbacks(token)

        except Exception as e:
            self.logger.error(f"Error processing PumpPortal token: {e}")

    async def _process_pumpportal_trade(self, data: Dict[str, Any]):
        """Process a trade from PumpPortal WebSocket."""
        try:
            mint = data.get("mint")
            if not mint:
                return

            # Create PumpFunTrade object from PumpPortal data
            trade = PumpFunTrade(
                signature=data.get("signature", ""),
                mint=mint,
                trader=data.get("traderPublicKey", ""),
                is_buy=data.get("txType") == "buy",
                sol_amount=float(data.get("solAmount", 0)),
                token_amount=float(data.get("vTokensInBondingCurve", 0)),
                price_per_token=0.0,  # Calculate if needed
                market_cap_sol=float(data.get("marketCapSol", 0)),
                timestamp=get_current_datetime(),
                is_creation_trade=data.get("initialBuy", False),
            )

            # Get token info
            token = self.tracked_tokens.get(mint)
            if not token:
                # Create minimal token info if we don't have it
                token = PumpFunToken(
                    mint=mint,
                    name=data.get("name", "Unknown"),
                    symbol=data.get("symbol", "Unknown"),
                    description="",
                    creator="",
                    creation_time=get_current_datetime(),
                    market_cap=float(data.get("marketCapSol", 0)),
                    price_sol=0.0,
                    volume_24h=0.0,
                    holder_count=0,
                    is_graduated=False,
                    bonding_curve=data.get("bondingCurveKey", ""),
                    metadata_uri=data.get("uri", ""),
                )
                self.tracked_tokens[mint] = token

            # Check if this is an early trade
            is_early_trade = trade.is_creation_trade or (
                token.creation_time
                and (get_current_datetime() - token.creation_time).total_seconds() < 300
            )

            if is_early_trade:
                action = "bought" if trade.is_buy else "sold"
                self.logger.info(
                    f"💰 Early trade: {trade.trader[:8]}... {action} "
                    f"{trade.sol_amount:.4f} SOL of {token.symbol}"
                )

                # Send Telegram notification for significant early trades
                if trade.sol_amount >= 1.0:  # Only notify for trades >= 1 SOL
                    await self._send_early_trade_notification(trade, token)

            # Notify callbacks
            await self._notify_trade_callbacks(trade, token, is_early_trade)

        except Exception as e:
            self.logger.error(f"Error processing PumpPortal trade: {e}")

    async def _log_statistics(self):
        """Log monitoring statistics periodically."""
        try:
            while self.is_monitoring:
                await asyncio.sleep(300)  # Log every 5 minutes

                self.logger.info(
                    f"📊 PumpPortal Stats - Tokens: {self.tokens_detected}, "
                    f"Trades: {self.trades_detected}, "
                    f"Tracked: {len(self.tracked_tokens)}"
                )

        except asyncio.CancelledError:
            self.logger.info("Statistics logging cancelled")

    async def _send_new_token_notification(self, token: PumpFunToken):
        """Send Telegram notification for new token launch."""
        try:
            from src.telegram_bot.notification_manager import notification_manager

            # Create notification data
            notification_data = {
                "mint": token.mint,
                "name": token.name,
                "symbol": token.symbol,
                "creator": token.creator,
                "market_cap": token.market_cap,
                "creation_time": (
                    token.creation_time.isoformat() if token.creation_time else None
                ),
                "bonding_curve": token.bonding_curve,
            }

            await notification_manager.notify_pump_launch(notification_data)

        except Exception as e:
            self.logger.error(f"Error sending new token notification: {e}")

    async def _send_early_trade_notification(
        self, trade: PumpFunTrade, token: PumpFunToken
    ):
        """Send Telegram notification for early trade."""
        try:
            from src.telegram_bot.notification_manager import notification_manager

            # Create notification data
            notification_data = {
                "signature": trade.signature,
                "mint": trade.mint,
                "token_name": token.name,
                "token_symbol": token.symbol,
                "trader": trade.trader,
                "is_buy": trade.is_buy,
                "sol_amount": trade.sol_amount,
                "token_amount": trade.token_amount,
                "market_cap": trade.market_cap_sol,
                "is_creation_trade": trade.is_creation_trade,
                "timestamp": trade.timestamp.isoformat() if trade.timestamp else None,
            }

            await notification_manager.notify_early_trade(notification_data)

        except Exception as e:
            self.logger.error(f"Error sending early trade notification: {e}")

    async def _monitor_pump_fun_transactions(self):
        """Monitor pump.fun program transactions via WebSocket."""
        try:
            # Subscribe to pump.fun program logs
            await solana_client.subscribe_to_logs(
                callback=self._handle_pump_fun_log, mentions=[self.PUMP_FUN_PROGRAM]
            )

        except Exception as e:
            self.logger.error(f"Error monitoring pump.fun transactions: {e}")

    # OLD REST API METHOD - DISABLED (503 errors)
    # Now using PumpPortal WebSocket for real-time data
    async def get_latest_tokens(self, limit: int = 10) -> List[PumpFunToken]:
        """Get latest tokens from tracked tokens (WebSocket data)."""
        # Return recent launches sorted by creation time
        recent = sorted(
            self.recent_launches, key=lambda t: t.creation_time, reverse=True
        )
        return recent[:limit]

    async def _fetch_trending_tokens(self) -> List[Dict[str, Any]]:
        """Fetch trending tokens from pump.fun API."""
        try:
            await self._rate_limit()

            async with aiohttp.ClientSession() as session:
                url = f"{self.PUMP_FUN_API_BASE}/coins"
                params = {
                    "offset": 0,
                    "limit": 20,
                    "sort": "volume_24h",
                    "order": "DESC",
                }

                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("coins", [])
                    else:
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching trending tokens: {e}")
            return []

    async def _fetch_token_trades(
        self, mint: str, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Fetch recent trades for a specific token."""
        try:
            await self._rate_limit()

            async with aiohttp.ClientSession() as session:
                url = f"{self.PUMP_FUN_API_BASE}/trades/{mint}"
                params = {"limit": limit}

                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("trades", [])
                    else:
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching token trades for {mint}: {e}")
            return []

    async def _process_new_token(self, token_data: Dict[str, Any]):
        """Process a newly discovered token."""
        try:
            mint = token_data.get("mint")
            if not mint or mint in self.tracked_tokens:
                return

            # Create PumpFunToken object
            token = PumpFunToken(
                mint=mint,
                name=token_data.get("name", ""),
                symbol=token_data.get("symbol", ""),
                description=token_data.get("description", ""),
                creator=token_data.get("creator", ""),
                creation_time=datetime.fromtimestamp(
                    token_data.get("created_timestamp", 0)
                ),
                market_cap=float(token_data.get("market_cap", 0)),
                price_sol=float(token_data.get("price", 0)),
                volume_24h=float(token_data.get("volume_24h", 0)),
                holder_count=int(token_data.get("holder_count", 0)),
                is_graduated=token_data.get("complete", False),
                bonding_curve=token_data.get("bonding_curve", ""),
                metadata_uri=token_data.get("metadata_uri", ""),
            )

            # Track the token
            self.tracked_tokens[mint] = token
            self.recent_launches.append(token)
            self.launch_detection_times[mint] = get_current_datetime()

            # Keep only recent launches (last 24 hours)
            cutoff_time = get_current_datetime() - timedelta(hours=24)
            self.recent_launches = [
                t for t in self.recent_launches if t.creation_time >= cutoff_time
            ]

            self.logger.info(
                f"New pump.fun token detected: {token.symbol} ({mint[:8]}...)"
            )

            # Notify callbacks
            await self._notify_token_callbacks(token)

            # Start monitoring trades for this token
            asyncio.create_task(self._monitor_token_trades(token))

        except Exception as e:
            self.logger.error(f"Error processing new token: {e}")

    async def _process_trending_token(self, token_data: Dict[str, Any]):
        """Process a trending token."""
        try:
            mint = token_data.get("mint")
            if not mint:
                return

            # Update existing token data or create new
            if mint in self.tracked_tokens:
                token = self.tracked_tokens[mint]
                # Update dynamic fields
                token.market_cap = float(token_data.get("market_cap", 0))
                token.price_sol = float(token_data.get("price", 0))
                token.volume_24h = float(token_data.get("volume_24h", 0))
                token.holder_count = int(token_data.get("holder_count", 0))
                token.is_graduated = token_data.get("complete", False)
            else:
                # New trending token
                await self._process_new_token(token_data)

        except Exception as e:
            self.logger.error(f"Error processing trending token: {e}")

    async def _monitor_token_trades(self, token: PumpFunToken):
        """Monitor trades for a specific token."""
        try:
            # Monitor for the first hour after launch
            end_time = token.creation_time + timedelta(hours=1)

            while get_current_datetime() < end_time and self.is_monitoring:
                try:
                    trades = await self._fetch_token_trades(token.mint, limit=50)

                    for trade_data in trades:
                        await self._process_token_trade(trade_data, token)

                    await asyncio.sleep(10)  # Check every 10 seconds

                except Exception as e:
                    self.logger.error(f"Error monitoring trades for {token.mint}: {e}")
                    await asyncio.sleep(30)

        except Exception as e:
            self.logger.error(f"Error in token trade monitoring: {e}")

    async def _process_token_trade(
        self, trade_data: Dict[str, Any], token: PumpFunToken
    ):
        """Process a token trade."""
        try:
            # Create PumpFunTrade object
            trade = PumpFunTrade(
                signature=trade_data.get("signature", ""),
                mint=token.mint,
                trader=trade_data.get("user", ""),
                is_buy=trade_data.get("is_buy", True),
                sol_amount=float(trade_data.get("sol_amount", 0)),
                token_amount=float(trade_data.get("token_amount", 0)),
                price_per_token=float(trade_data.get("price", 0)),
                market_cap_sol=float(trade_data.get("market_cap", 0)),
                timestamp=datetime.fromtimestamp(trade_data.get("timestamp", 0)),
                is_creation_trade=trade_data.get("is_creation", False),
            )

            # Check if this is an early/snipe trade
            time_since_launch = (trade.timestamp - token.creation_time).total_seconds()
            is_early_trade = time_since_launch < 300  # Within 5 minutes of launch

            if is_early_trade:
                self.logger.info(
                    f"Early trade detected: {trade.trader[:8]}... "
                    f"{'bought' if trade.is_buy else 'sold'} {trade.sol_amount:.4f} SOL "
                    f"of {token.symbol} {time_since_launch:.1f}s after launch"
                )

            # Notify callbacks
            await self._notify_trade_callbacks(trade, token, is_early_trade)

        except Exception as e:
            self.logger.error(f"Error processing token trade: {e}")

    async def _handle_pump_fun_log(self, params: Dict[str, Any]):
        """Handle pump.fun program log notifications."""
        try:
            if "result" not in params:
                return

            result = params["result"]
            if "value" not in result:
                return

            log_data = result["value"]
            signature = log_data.get("signature")

            if not signature:
                return

            # Process the transaction for pump.fun specific data
            await self._process_pump_fun_transaction(signature)

        except Exception as e:
            self.logger.error(f"Error handling pump.fun log: {e}")

    async def _process_pump_fun_transaction(self, signature: str):
        """Process a pump.fun transaction."""
        try:
            # Get transaction details
            transaction_data = await solana_client.get_transaction(signature)

            if not transaction_data:
                return

            # Parse for pump.fun specific instructions
            # This would involve parsing the instruction data to identify:
            # - Token creation
            # - Buy/sell transactions
            # - Bonding curve interactions

            # For now, log that we detected a pump.fun transaction
            self.logger.debug(f"Pump.fun transaction detected: {signature}")

        except Exception as e:
            self.logger.error(f"Error processing pump.fun transaction {signature}: {e}")

    async def _notify_token_callbacks(self, token: PumpFunToken):
        """Notify callbacks about new token."""
        for callback in self.token_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(token)
                else:
                    callback(token)
            except Exception as e:
                self.logger.error(f"Error in token callback: {e}")

    async def _notify_trade_callbacks(
        self, trade: PumpFunTrade, token: PumpFunToken, is_early: bool
    ):
        """Notify callbacks about token trade."""
        for callback in self.trade_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(trade, token, is_early)
                else:
                    callback(trade, token, is_early)
            except Exception as e:
                self.logger.error(f"Error in trade callback: {e}")

    async def _rate_limit(self):
        """Implement rate limiting for API calls."""
        current_time = asyncio.get_event_loop().time()
        time_since_last_call = current_time - self.last_api_call

        if time_since_last_call < self.api_call_interval:
            await asyncio.sleep(self.api_call_interval - time_since_last_call)

        self.last_api_call = asyncio.get_event_loop().time()

    async def _cleanup_old_data(self):
        """Clean up old tracking data."""
        try:
            while self.is_monitoring:
                try:
                    cutoff_time = get_current_datetime() - timedelta(hours=24)

                    # Clean up old launch detection times
                    old_mints = [
                        mint
                        for mint, detection_time in self.launch_detection_times.items()
                        if detection_time < cutoff_time
                    ]

                    for mint in old_mints:
                        del self.launch_detection_times[mint]
                        if mint in self.tracked_tokens:
                            del self.tracked_tokens[mint]

                    if old_mints:
                        self.logger.info(
                            f"Cleaned up {len(old_mints)} old token records"
                        )

                    await asyncio.sleep(3600)  # Clean up every hour

                except Exception as e:
                    self.logger.error(f"Error in cleanup: {e}")
                    await asyncio.sleep(3600)

        except asyncio.CancelledError:
            self.logger.info("Cleanup task cancelled")

    def get_recent_launches(self, hours: int = 1) -> List[PumpFunToken]:
        """Get tokens launched in the last N hours."""
        cutoff_time = get_current_datetime() - timedelta(hours=hours)
        return [
            token
            for token in self.recent_launches
            if token.creation_time >= cutoff_time
        ]

    def get_token_info(self, mint: str) -> Optional[PumpFunToken]:
        """Get information about a specific token."""
        return self.tracked_tokens.get(mint)


# Global pump.fun monitor instance
pumpfun_monitor = PumpFunMonitor()
