"""
Pump.fun specific monitoring for new token launches and early trading activity.
"""

import asyncio
import json
import aiohttp
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.blockchain.solana_client import solana_client
from src.blockchain.transaction_monitor import SwapTransaction
from src.config.settings import settings
from src.utils.logger import LoggerMixin
from src.utils.helpers import get_current_datetime, is_valid_solana_address


@dataclass
class PumpFunToken:
    """Represents a pump.fun token."""
    mint: str
    name: str
    symbol: str
    description: str
    creator: str
    creation_time: datetime
    market_cap: float
    price_sol: float
    volume_24h: float
    holder_count: int
    is_graduated: bool
    bonding_curve: str
    metadata_uri: str


@dataclass
class PumpFunTrade:
    """Represents a pump.fun trade."""
    signature: str
    mint: str
    trader: str
    is_buy: bool
    sol_amount: float
    token_amount: float
    price_per_token: float
    market_cap_sol: float
    timestamp: datetime
    is_creation_trade: bool = False


class PumpFunMonitor(LoggerMixin):
    """Monitors pump.fun for new token launches and trading activity."""
    
    def __init__(self):
        super().__init__()
        self.is_monitoring = False
        
        # Pump.fun specific constants
        self.PUMP_FUN_PROGRAM = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
        self.PUMP_FUN_API_BASE = "https://frontend-api.pump.fun"
        
        # Token tracking
        self.tracked_tokens = {}  # mint -> PumpFunToken
        self.recent_launches = []  # Recent token launches
        self.token_callbacks = []
        self.trade_callbacks = []
        
        # Performance tracking
        self.launch_detection_times = {}  # mint -> detection_time
        
        # Rate limiting
        self.last_api_call = 0
        self.api_call_interval = 1.0  # 1 second between API calls
        
    async def start_monitoring(self):
        """Start monitoring pump.fun."""
        if self.is_monitoring:
            self.logger.warning("Pump.fun monitoring is already running")
            return
        
        try:
            self.is_monitoring = True
            self.logger.info("Starting pump.fun monitoring...")
            
            # Start monitoring tasks
            tasks = [
                asyncio.create_task(self._monitor_new_tokens()),
                asyncio.create_task(self._monitor_trending_tokens()),
                asyncio.create_task(self._monitor_pump_fun_transactions()),
                asyncio.create_task(self._cleanup_old_data())
            ]
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"Error in pump.fun monitoring: {e}")
            raise
    
    async def stop_monitoring(self):
        """Stop monitoring pump.fun."""
        self.is_monitoring = False
        self.logger.info("Pump.fun monitoring stopped")
    
    def add_token_callback(self, callback):
        """Add callback for new token launches."""
        self.token_callbacks.append(callback)
    
    def add_trade_callback(self, callback):
        """Add callback for pump.fun trades."""
        self.trade_callbacks.append(callback)
    
    async def _monitor_new_tokens(self):
        """Monitor for new token launches on pump.fun."""
        try:
            while self.is_monitoring:
                try:
                    # Get latest tokens from pump.fun API
                    new_tokens = await self._fetch_latest_tokens()
                    
                    for token_data in new_tokens:
                        await self._process_new_token(token_data)
                    
                    await asyncio.sleep(5)  # Check every 5 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error monitoring new tokens: {e}")
                    await asyncio.sleep(10)
                    
        except asyncio.CancelledError:
            self.logger.info("New token monitoring cancelled")
    
    async def _monitor_trending_tokens(self):
        """Monitor trending tokens for high activity."""
        try:
            while self.is_monitoring:
                try:
                    # Get trending tokens
                    trending = await self._fetch_trending_tokens()
                    
                    for token_data in trending:
                        await self._process_trending_token(token_data)
                    
                    await asyncio.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error monitoring trending tokens: {e}")
                    await asyncio.sleep(30)
                    
        except asyncio.CancelledError:
            self.logger.info("Trending token monitoring cancelled")
    
    async def _monitor_pump_fun_transactions(self):
        """Monitor pump.fun program transactions via WebSocket."""
        try:
            # Subscribe to pump.fun program logs
            await solana_client.subscribe_to_logs(
                callback=self._handle_pump_fun_log,
                mentions=[self.PUMP_FUN_PROGRAM]
            )
            
        except Exception as e:
            self.logger.error(f"Error monitoring pump.fun transactions: {e}")
    
    async def _fetch_latest_tokens(self) -> List[Dict[str, Any]]:
        """Fetch latest tokens from pump.fun API."""
        try:
            await self._rate_limit()
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.PUMP_FUN_API_BASE}/coins"
                params = {
                    "offset": 0,
                    "limit": 50,
                    "sort": "created_timestamp",
                    "order": "DESC"
                }
                
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("coins", [])
                    else:
                        self.logger.warning(f"API request failed: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Error fetching latest tokens: {e}")
            return []
    
    async def _fetch_trending_tokens(self) -> List[Dict[str, Any]]:
        """Fetch trending tokens from pump.fun API."""
        try:
            await self._rate_limit()
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.PUMP_FUN_API_BASE}/coins"
                params = {
                    "offset": 0,
                    "limit": 20,
                    "sort": "volume_24h",
                    "order": "DESC"
                }
                
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("coins", [])
                    else:
                        return []
                        
        except Exception as e:
            self.logger.error(f"Error fetching trending tokens: {e}")
            return []
    
    async def _fetch_token_trades(self, mint: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch recent trades for a specific token."""
        try:
            await self._rate_limit()
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.PUMP_FUN_API_BASE}/trades/{mint}"
                params = {"limit": limit}
                
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("trades", [])
                    else:
                        return []
                        
        except Exception as e:
            self.logger.error(f"Error fetching token trades for {mint}: {e}")
            return []
    
    async def _process_new_token(self, token_data: Dict[str, Any]):
        """Process a newly discovered token."""
        try:
            mint = token_data.get("mint")
            if not mint or mint in self.tracked_tokens:
                return
            
            # Create PumpFunToken object
            token = PumpFunToken(
                mint=mint,
                name=token_data.get("name", ""),
                symbol=token_data.get("symbol", ""),
                description=token_data.get("description", ""),
                creator=token_data.get("creator", ""),
                creation_time=datetime.fromtimestamp(token_data.get("created_timestamp", 0)),
                market_cap=float(token_data.get("market_cap", 0)),
                price_sol=float(token_data.get("price", 0)),
                volume_24h=float(token_data.get("volume_24h", 0)),
                holder_count=int(token_data.get("holder_count", 0)),
                is_graduated=token_data.get("complete", False),
                bonding_curve=token_data.get("bonding_curve", ""),
                metadata_uri=token_data.get("metadata_uri", "")
            )
            
            # Track the token
            self.tracked_tokens[mint] = token
            self.recent_launches.append(token)
            self.launch_detection_times[mint] = get_current_datetime()
            
            # Keep only recent launches (last 24 hours)
            cutoff_time = get_current_datetime() - timedelta(hours=24)
            self.recent_launches = [
                t for t in self.recent_launches 
                if t.creation_time >= cutoff_time
            ]
            
            self.logger.info(f"New pump.fun token detected: {token.symbol} ({mint[:8]}...)")
            
            # Notify callbacks
            await self._notify_token_callbacks(token)
            
            # Start monitoring trades for this token
            asyncio.create_task(self._monitor_token_trades(token))
            
        except Exception as e:
            self.logger.error(f"Error processing new token: {e}")
    
    async def _process_trending_token(self, token_data: Dict[str, Any]):
        """Process a trending token."""
        try:
            mint = token_data.get("mint")
            if not mint:
                return
            
            # Update existing token data or create new
            if mint in self.tracked_tokens:
                token = self.tracked_tokens[mint]
                # Update dynamic fields
                token.market_cap = float(token_data.get("market_cap", 0))
                token.price_sol = float(token_data.get("price", 0))
                token.volume_24h = float(token_data.get("volume_24h", 0))
                token.holder_count = int(token_data.get("holder_count", 0))
                token.is_graduated = token_data.get("complete", False)
            else:
                # New trending token
                await self._process_new_token(token_data)
            
        except Exception as e:
            self.logger.error(f"Error processing trending token: {e}")
    
    async def _monitor_token_trades(self, token: PumpFunToken):
        """Monitor trades for a specific token."""
        try:
            # Monitor for the first hour after launch
            end_time = token.creation_time + timedelta(hours=1)
            
            while get_current_datetime() < end_time and self.is_monitoring:
                try:
                    trades = await self._fetch_token_trades(token.mint, limit=50)
                    
                    for trade_data in trades:
                        await self._process_token_trade(trade_data, token)
                    
                    await asyncio.sleep(10)  # Check every 10 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error monitoring trades for {token.mint}: {e}")
                    await asyncio.sleep(30)
            
        except Exception as e:
            self.logger.error(f"Error in token trade monitoring: {e}")
    
    async def _process_token_trade(self, trade_data: Dict[str, Any], token: PumpFunToken):
        """Process a token trade."""
        try:
            # Create PumpFunTrade object
            trade = PumpFunTrade(
                signature=trade_data.get("signature", ""),
                mint=token.mint,
                trader=trade_data.get("user", ""),
                is_buy=trade_data.get("is_buy", True),
                sol_amount=float(trade_data.get("sol_amount", 0)),
                token_amount=float(trade_data.get("token_amount", 0)),
                price_per_token=float(trade_data.get("price", 0)),
                market_cap_sol=float(trade_data.get("market_cap", 0)),
                timestamp=datetime.fromtimestamp(trade_data.get("timestamp", 0)),
                is_creation_trade=trade_data.get("is_creation", False)
            )
            
            # Check if this is an early/snipe trade
            time_since_launch = (trade.timestamp - token.creation_time).total_seconds()
            is_early_trade = time_since_launch < 300  # Within 5 minutes of launch
            
            if is_early_trade:
                self.logger.info(
                    f"Early trade detected: {trade.trader[:8]}... "
                    f"{'bought' if trade.is_buy else 'sold'} {trade.sol_amount:.4f} SOL "
                    f"of {token.symbol} {time_since_launch:.1f}s after launch"
                )
            
            # Notify callbacks
            await self._notify_trade_callbacks(trade, token, is_early_trade)
            
        except Exception as e:
            self.logger.error(f"Error processing token trade: {e}")
    
    async def _handle_pump_fun_log(self, params: Dict[str, Any]):
        """Handle pump.fun program log notifications."""
        try:
            if "result" not in params:
                return
            
            result = params["result"]
            if "value" not in result:
                return
            
            log_data = result["value"]
            signature = log_data.get("signature")
            
            if not signature:
                return
            
            # Process the transaction for pump.fun specific data
            await self._process_pump_fun_transaction(signature)
            
        except Exception as e:
            self.logger.error(f"Error handling pump.fun log: {e}")
    
    async def _process_pump_fun_transaction(self, signature: str):
        """Process a pump.fun transaction."""
        try:
            # Get transaction details
            transaction_data = await solana_client.get_transaction(signature)
            
            if not transaction_data:
                return
            
            # Parse for pump.fun specific instructions
            # This would involve parsing the instruction data to identify:
            # - Token creation
            # - Buy/sell transactions
            # - Bonding curve interactions
            
            # For now, log that we detected a pump.fun transaction
            self.logger.debug(f"Pump.fun transaction detected: {signature}")
            
        except Exception as e:
            self.logger.error(f"Error processing pump.fun transaction {signature}: {e}")
    
    async def _notify_token_callbacks(self, token: PumpFunToken):
        """Notify callbacks about new token."""
        for callback in self.token_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(token)
                else:
                    callback(token)
            except Exception as e:
                self.logger.error(f"Error in token callback: {e}")
    
    async def _notify_trade_callbacks(self, trade: PumpFunTrade, token: PumpFunToken, is_early: bool):
        """Notify callbacks about token trade."""
        for callback in self.trade_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(trade, token, is_early)
                else:
                    callback(trade, token, is_early)
            except Exception as e:
                self.logger.error(f"Error in trade callback: {e}")
    
    async def _rate_limit(self):
        """Implement rate limiting for API calls."""
        current_time = asyncio.get_event_loop().time()
        time_since_last_call = current_time - self.last_api_call
        
        if time_since_last_call < self.api_call_interval:
            await asyncio.sleep(self.api_call_interval - time_since_last_call)
        
        self.last_api_call = asyncio.get_event_loop().time()
    
    async def _cleanup_old_data(self):
        """Clean up old tracking data."""
        try:
            while self.is_monitoring:
                try:
                    cutoff_time = get_current_datetime() - timedelta(hours=24)
                    
                    # Clean up old launch detection times
                    old_mints = [
                        mint for mint, detection_time in self.launch_detection_times.items()
                        if detection_time < cutoff_time
                    ]
                    
                    for mint in old_mints:
                        del self.launch_detection_times[mint]
                        if mint in self.tracked_tokens:
                            del self.tracked_tokens[mint]
                    
                    if old_mints:
                        self.logger.info(f"Cleaned up {len(old_mints)} old token records")
                    
                    await asyncio.sleep(3600)  # Clean up every hour
                    
                except Exception as e:
                    self.logger.error(f"Error in cleanup: {e}")
                    await asyncio.sleep(3600)
                    
        except asyncio.CancelledError:
            self.logger.info("Cleanup task cancelled")
    
    def get_recent_launches(self, hours: int = 1) -> List[PumpFunToken]:
        """Get tokens launched in the last N hours."""
        cutoff_time = get_current_datetime() - timedelta(hours=hours)
        return [
            token for token in self.recent_launches
            if token.creation_time >= cutoff_time
        ]
    
    def get_token_info(self, mint: str) -> Optional[PumpFunToken]:
        """Get information about a specific token."""
        return self.tracked_tokens.get(mint)


# Global pump.fun monitor instance
pumpfun_monitor = PumpFunMonitor()
