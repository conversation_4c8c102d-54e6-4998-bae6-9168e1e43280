"""
Database models for the Solana Trading Bot.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, 
    Text, JSON, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Wallet(Base):
    """Model for tracked wallets."""
    __tablename__ = 'wallets'
    
    id = Column(Integer, primary_key=True)
    address = Column(String(44), unique=True, nullable=False, index=True)
    
    # Classification
    trader_type = Column(String(20))  # sniper, insider, dev, whale
    confidence_score = Column(Float, default=0.0)
    
    # Performance metrics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    total_pnl_sol = Column(Float, default=0.0)
    total_roi = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    avg_hold_time_hours = Column(Float, default=0.0)
    max_position_size_sol = Column(Float, default=0.0)
    
    # Tracking status
    is_active = Column(Boolean, default=True)
    is_copy_trading = Column(Boolean, default=False)
    last_activity = Column(DateTime)
    
    # Metadata
    first_seen = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    notes = Column(Text)
    
    # Relationships
    transactions = relationship("Transaction", back_populates="wallet")
    trades = relationship("Trade", back_populates="wallet")
    copy_trades = relationship("CopyTrade", back_populates="source_wallet")


class Token(Base):
    """Model for tracked tokens."""
    __tablename__ = 'tokens'
    
    id = Column(Integer, primary_key=True)
    mint_address = Column(String(44), unique=True, nullable=False, index=True)
    
    # Token metadata
    symbol = Column(String(20))
    name = Column(String(100))
    decimals = Column(Integer)
    
    # Market data
    current_price_sol = Column(Float)
    market_cap_sol = Column(Float)
    volume_24h_sol = Column(Float)
    
    # Token characteristics
    is_memecoin = Column(Boolean, default=False)
    creation_time = Column(DateTime)
    creator_address = Column(String(44))
    
    # Metadata
    first_seen = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    transactions = relationship("Transaction", back_populates="token")
    trades = relationship("Trade", back_populates="token")


class Transaction(Base):
    """Model for blockchain transactions."""
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True)
    signature = Column(String(88), unique=True, nullable=False, index=True)
    
    # Transaction details
    wallet_id = Column(Integer, ForeignKey('wallets.id'), nullable=False)
    token_id = Column(Integer, ForeignKey('tokens.id'), nullable=False)
    
    # Transaction data
    transaction_type = Column(String(10))  # buy, sell
    amount_sol = Column(Float, nullable=False)
    token_amount = Column(Float, nullable=False)
    price_per_token = Column(Float, nullable=False)
    
    # DEX information
    dex_program = Column(String(44))
    pool_address = Column(String(44))
    
    # Timing
    block_time = Column(DateTime, nullable=False)
    slot = Column(Integer)
    
    # Analysis
    is_snipe = Column(Boolean, default=False)
    speed_score = Column(Float)  # How fast after token creation
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    raw_data = Column(JSON)
    
    # Relationships
    wallet = relationship("Wallet", back_populates="transactions")
    token = relationship("Token", back_populates="transactions")
    
    # Indexes
    __table_args__ = (
        Index('idx_wallet_token_time', 'wallet_id', 'token_id', 'block_time'),
        Index('idx_transaction_type_time', 'transaction_type', 'block_time'),
    )


class Trade(Base):
    """Model for completed trades (buy + sell)."""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True)
    
    # Trade identification
    wallet_id = Column(Integer, ForeignKey('wallets.id'), nullable=False)
    token_id = Column(Integer, ForeignKey('tokens.id'), nullable=False)
    
    # Entry transaction
    entry_signature = Column(String(88), nullable=False)
    entry_time = Column(DateTime, nullable=False)
    entry_price = Column(Float, nullable=False)
    entry_amount_sol = Column(Float, nullable=False)
    
    # Exit transaction
    exit_signature = Column(String(88))
    exit_time = Column(DateTime)
    exit_price = Column(Float)
    exit_amount_sol = Column(Float)
    
    # Trade metrics
    pnl_sol = Column(Float)
    roi_percentage = Column(Float)
    hold_time_hours = Column(Float)
    
    # Trade classification
    is_complete = Column(Boolean, default=False)
    trade_type = Column(String(20))  # snipe, swing, scalp
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    wallet = relationship("Wallet", back_populates="trades")
    token = relationship("Token", back_populates="trades")
    
    # Indexes
    __table_args__ = (
        Index('idx_wallet_roi', 'wallet_id', 'roi_percentage'),
        Index('idx_trade_complete_time', 'is_complete', 'entry_time'),
    )


class CopyTrade(Base):
    """Model for copy trades executed by the bot."""
    __tablename__ = 'copy_trades'
    
    id = Column(Integer, primary_key=True)
    
    # Source information
    source_wallet_id = Column(Integer, ForeignKey('wallets.id'), nullable=False)
    source_signature = Column(String(88), nullable=False)
    
    # Our trade details
    our_signature = Column(String(88))
    token_mint = Column(String(44), nullable=False)
    
    # Trade execution
    trade_type = Column(String(10))  # buy, sell
    intended_amount_sol = Column(Float, nullable=False)
    actual_amount_sol = Column(Float)
    token_amount = Column(Float)
    execution_price = Column(Float)
    
    # Timing
    source_time = Column(DateTime, nullable=False)
    execution_time = Column(DateTime)
    delay_seconds = Column(Float)
    
    # Status and results
    status = Column(String(20), default='pending')  # pending, executed, failed
    error_message = Column(Text)
    
    # Risk management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    position_size_multiplier = Column(Float, default=1.0)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    source_wallet = relationship("Wallet", back_populates="copy_trades")


class PerformanceMetric(Base):
    """Model for storing performance metrics over time."""
    __tablename__ = 'performance_metrics'
    
    id = Column(Integer, primary_key=True)
    
    # Metric identification
    metric_type = Column(String(50), nullable=False)  # wallet_performance, bot_performance
    entity_id = Column(String(50))  # wallet_id or 'bot'
    
    # Time period
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Metrics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    total_pnl_sol = Column(Float, default=0.0)
    total_roi = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    
    # Additional data
    metadata = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_metric_type_entity', 'metric_type', 'entity_id'),
        Index('idx_metric_period', 'period_start', 'period_end'),
    )


class BotState(Base):
    """Model for storing bot state and configuration."""
    __tablename__ = 'bot_state'
    
    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(JSON)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
