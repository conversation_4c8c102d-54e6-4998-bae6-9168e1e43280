"""
Configuration management for the Solana Trading Bot.
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class SolanaConfig(BaseModel):
    # Primary RPC (Helius recommended for better performance)
    rpc_url: str = "https://mainnet.helius-rpc.com"
    ws_url: str = "wss://mainnet.helius-rpc.com"

    # Fallback to public RPC if Helius API key not available
    fallback_rpc_url: str = "https://api.mainnet-beta.solana.com"
    fallback_ws_url: str = "wss://api.mainnet-beta.solana.com"

    # Enhanced WebSocket for real-time data (Helius)
    enhanced_ws_url: str = "wss://atlas-mainnet.helius-rpc.com"

    # Staked connection for priority transactions
    staked_rpc_url: str = "https://staked.helius-rpc.com"

    commitment: str = "confirmed"

    # Network settings
    network: str = "mainnet"  # mainnet, devnet, testnet
    max_retries: int = 3
    timeout: int = 30


class DatabaseConfig(BaseModel):
    url: str = "sqlite:///solana_trading_bot.db"
    echo: bool = False


class CopyTradingConfig(BaseModel):
    enabled: bool = True
    max_position_size_sol: float = 1.0
    min_position_size_sol: float = 0.01
    slippage_tolerance: float = 0.05


class RiskManagementConfig(BaseModel):
    stop_loss_percentage: float = 0.15
    take_profit_percentage: float = 2.0
    max_daily_loss_sol: float = 5.0
    max_concurrent_positions: int = 10


class PositionSizingConfig(BaseModel):
    base_size_sol: float = 0.1
    size_multiplier_by_confidence: Dict[str, float] = {
        "high": 2.0,
        "medium": 1.0,
        "low": 0.5,
    }


class TradingConfig(BaseModel):
    copy_trading: CopyTradingConfig = CopyTradingConfig()
    risk_management: RiskManagementConfig = RiskManagementConfig()
    position_sizing: PositionSizingConfig = PositionSizingConfig()


class TraderClassificationConfig(BaseModel):
    sniper: Dict[str, float] = {
        "min_speed_score": 0.8,
        "min_roi": 5.0,
        "max_hold_time_hours": 24,
    }
    insider: Dict[str, float] = {
        "min_early_entry_score": 0.9,
        "min_roi": 20.0,
        "unusual_timing_threshold": 0.8,
    }
    whale: Dict[str, float] = {"min_position_size_sol": 10.0, "min_market_impact": 0.05}
    dev: Dict[str, float] = {"min_token_creation_correlation": 0.7, "min_roi": 50.0}


class WalletTrackingConfig(BaseModel):
    min_roi_threshold: float = 10.0
    min_win_rate: float = 0.6
    min_trades: int = 5
    classification: TraderClassificationConfig = TraderClassificationConfig()


class TransactionMonitoringConfig(BaseModel):
    enabled: bool = True
    dex_programs: list = [
        "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",  # Raydium
        "9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP",  # Orca
        "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",  # Jupiter
        "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",  # Pump.fun
    ]


class PumpFunMonitoringConfig(BaseModel):
    enabled: bool = True
    # Updated to use working endpoints (tested 2025-05-25)
    api_base_url: str = "https://frontend-api-v3.pump.fun"  # Only V3 is working
    primary_endpoint: str = (
        "https://frontend-api-v3.pump.fun/coins"  # Use with offset/limit
    )
    fallback_endpoint: str = (
        "https://frontend-api-v3.pump.fun/coins/latest"  # Single coin
    )
    working_params: Dict[str, str] = {"offset": "0", "limit": "10"}  # Required params

    # Broken endpoints - DO NOT USE
    api_base_url_v2: str = "https://frontend-api-v2.pump.fun"  # 503 Service Unavailable
    api_base_url_v1: str = "https://frontend-api.pump.fun"  # 503 Service Unavailable
    live_coins_endpoint: str = (
        "https://frontend-api-v3.pump.fun/coins/currently-live"  # 500 Error
    )

    new_token_check_interval: int = 5  # seconds
    trending_check_interval: int = 30  # seconds
    snipe_time_threshold: int = 60  # seconds to qualify as snipe
    early_trade_threshold: int = 300  # seconds to qualify as early trade
    monitor_duration_hours: int = 1  # how long to monitor each token after launch


class PerformanceMonitoringConfig(BaseModel):
    update_interval_seconds: int = 300
    cleanup_old_data_days: int = 30


class MonitoringConfig(BaseModel):
    transaction_monitoring: TransactionMonitoringConfig = TransactionMonitoringConfig()
    pumpfun_monitoring: PumpFunMonitoringConfig = PumpFunMonitoringConfig()
    performance_monitoring: PerformanceMonitoringConfig = PerformanceMonitoringConfig()


class APIConfig(BaseModel):
    """API configuration with accurate URLs from official documentation."""

    # Jupiter API - Updated with new hostnames (March 2025)
    jupiter: Dict[str, Any] = {
        # New endpoints (current)
        "quote_url": "https://lite-api.jup.ag/swap/v1/quote",
        "swap_url": "https://lite-api.jup.ag/swap/v1/swap",
        "price_url": "https://lite-api.jup.ag/price/v2",
        "tokens_url": "https://lite-api.jup.ag/tokens/v1/mints/tradable",
        "token_info_url": "https://lite-api.jup.ag/tokens/v1/token",
        # Legacy endpoints (being phased out by May 1, 2025)
        "legacy_quote_url": "https://quote-api.jup.ag/v6/quote",
        "legacy_swap_url": "https://quote-api.jup.ag/v6/swap",
        "legacy_price_url": "https://price.jup.ag/v6",
        # Trigger API (Limit Orders)
        "trigger_create_url": "https://lite-api.jup.ag/trigger/v1/createOrder",
        "trigger_execute_url": "https://lite-api.jup.ag/trigger/v1/execute",
        "trigger_cancel_url": "https://lite-api.jup.ag/trigger/v1/cancelOrder",
        "trigger_orders_url": "https://lite-api.jup.ag/trigger/v1/getTriggerOrders",
        "timeout": 10,
        "rate_limit_rpm": 60,  # Free tier
    }

    # Birdeye API - DISABLED (API requires paid subscription)
    birdeye: Dict[str, Any] = {
        "enabled": False,  # Disabled due to API access restrictions
        "base_url": "https://public-api.birdeye.so",
        "price_url": "https://public-api.birdeye.so/defi/price",
        "multi_price_url": "https://public-api.birdeye.so/defi/multi_price",
        "trades_token_url": "https://public-api.birdeye.so/defi/txs/token",
        "trades_pair_url": "https://public-api.birdeye.so/defi/txs/pair",
        "ohlcv_url": "https://public-api.birdeye.so/defi/ohlcv",
        "token_overview_url": "https://public-api.birdeye.so/defi/token_overview",
        "token_security_url": "https://public-api.birdeye.so/defi/token_security",
        "new_listings_url": "https://public-api.birdeye.so/defi/v2/tokens/new_listing",
        "top_traders_url": "https://public-api.birdeye.so/defi/v2/tokens/top_traders",
        "trending_url": "https://public-api.birdeye.so/defi/token_trending",
        "search_url": "https://public-api.birdeye.so/defi/v3/search",
        "websocket_url": "wss://public-api.birdeye.so/socket/solana",
        "timeout": 10,
        "rate_limit_rpm": 100,  # Starter tier
        "note": "Requires API key and paid subscription for access",
    }

    # DexScreener API - Verified endpoints
    dexscreener: Dict[str, Any] = {
        "base_url": "https://api.dexscreener.com",
        "pairs_url": "https://api.dexscreener.com/latest/dex/pairs",
        "tokens_url": "https://api.dexscreener.com/tokens/v1",
        "search_url": "https://api.dexscreener.com/latest/dex/search",
        "token_pairs_url": "https://api.dexscreener.com/token-pairs/v1",
        "profiles_url": "https://api.dexscreener.com/token-profiles/latest/v1",
        "boosts_url": "https://api.dexscreener.com/token-boosts/latest/v1",
        "timeout": 10,
        "rate_limit_rpm": 300,  # Pairs/tokens endpoints
    }

    # Pump.fun API - Updated with working endpoints (tested 2025-05-25)
    pumpfun: Dict[str, Any] = {
        # API versions status
        "base_url_v1": "https://frontend-api.pump.fun",  # 503 Service Unavailable
        "base_url_v2": "https://frontend-api-v2.pump.fun",  # 503 Service Unavailable
        "base_url_v3": "https://frontend-api-v3.pump.fun",  # Working
        # Working V3 endpoints (tested 2025-05-25)
        "coins_url": "https://frontend-api-v3.pump.fun/coins",  # Works with offset/limit params
        "coins_latest_url": "https://frontend-api-v3.pump.fun/coins/latest",  # Single latest coin
        "coins_featured_1h_url": "https://frontend-api-v3.pump.fun/coins/featured/1h",
        "coins_featured_24h_url": "https://frontend-api-v3.pump.fun/coins/featured/24h",
        "sol_price_url": "https://frontend-api-v3.pump.fun/sol-price",
        # Broken endpoints (as of 2025-05-25) - DO NOT USE
        "coins_live_url": "https://frontend-api-v3.pump.fun/coins/currently-live",  # 500 Internal Error
        "coins_search_url": "https://frontend-api-v3.pump.fun/coins/search",  # Not tested
        "trades_latest_url": "https://frontend-api-v3.pump.fun/trades/latest",  # Not tested
        "trades_all_url": "https://frontend-api-v3.pump.fun/trades/all",  # Not tested
        "candlesticks_url": "https://frontend-api-v3.pump.fun/candlesticks",  # Not tested
        # Recommended usage
        "primary_endpoint": "https://frontend-api-v3.pump.fun/coins",  # Use with offset/limit
        "fallback_endpoint": "https://frontend-api-v3.pump.fun/coins/latest",  # Single coin
        "working_params": {
            "offset": "0",
            "limit": "10",
        },  # Required for /coins endpoint
        "timeout": 10,
        "rate_limit_rpm": 60,  # Estimated
    }

    # Helius RPC - Verified endpoints
    helius: Dict[str, Any] = {
        "mainnet_url": "https://mainnet.helius-rpc.com",
        "devnet_url": "https://devnet.helius-rpc.com",
        "staked_url": "https://staked.helius-rpc.com",  # 50 credits per request
        "websocket_mainnet_url": "wss://mainnet.helius-rpc.com",
        "websocket_devnet_url": "wss://devnet.helius-rpc.com",
        "enhanced_ws_mainnet_url": "wss://atlas-mainnet.helius-rpc.com",
        "enhanced_ws_devnet_url": "wss://atlas-devnet.helius-rpc.com",
        "timeout": 30,
        "rate_limit_rpm": 1000,  # Developer tier
    }

    # QuickNode Pump.fun API (Alternative)
    quicknode: Dict[str, Any] = {
        "pumpfun_quote_url": "https://docs-demo.solana-mainnet.quiknode.pro/pump-fun/quote",
        "pumpfun_swap_url": "https://docs-demo.solana-mainnet.quiknode.pro/pump-fun/swap",
        "timeout": 10,
        "rate_limit_rpm": 300,
    }


class LoggingConfig(BaseModel):
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/trading_bot.log"
    max_file_size_mb: int = 100
    backup_count: int = 5


class SecurityConfig(BaseModel):
    private_key_env: str = "SOLANA_PRIVATE_KEY"
    encrypt_database: bool = False


class TelegramNotificationsConfig(BaseModel):
    enabled: bool = True
    rate_limits: Dict[str, int] = {
        "trade_notifications": 0,
        "sniper_alerts": 300,
        "pump_launches": 10,
        "risk_alerts": 60,
    }


class TelegramTradingConfig(BaseModel):
    enabled: bool = True
    require_confirmation: bool = True
    max_manual_trade_size: float = 1.0


class TelegramConfig(BaseModel):
    bot_token: str = "YOUR_TELEGRAM_BOT_TOKEN"
    authorized_users: List[int] = []
    notifications: TelegramNotificationsConfig = TelegramNotificationsConfig()
    trading: TelegramTradingConfig = TelegramTradingConfig()


class FeatureFlags(BaseModel):
    enable_copy_trading: bool = True
    enable_wallet_discovery: bool = True
    enable_performance_tracking: bool = True
    enable_risk_management: bool = True
    enable_notifications: bool = True
    enable_telegram_bot: bool = (
        False  # Disabled temporarily due to library compatibility
    )


class Settings(BaseSettings):
    """Main settings class that loads configuration from YAML and environment variables."""

    solana: SolanaConfig = SolanaConfig()
    database: DatabaseConfig = DatabaseConfig()
    trading: TradingConfig = TradingConfig()
    wallet_tracking: WalletTrackingConfig = WalletTrackingConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    apis: APIConfig = APIConfig()
    logging: LoggingConfig = LoggingConfig()
    security: SecurityConfig = SecurityConfig()
    telegram: TelegramConfig = TelegramConfig()
    features: FeatureFlags = FeatureFlags()

    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"
        extra = "ignore"  # Ignore extra fields from environment


def load_config(config_path: str = "config.yaml") -> Settings:
    """Load configuration from YAML file and environment variables."""

    # Load from YAML file if it exists
    config_data = {}
    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            config_data = yaml.safe_load(f) or {}

    # Create settings instance with YAML data
    settings = Settings(**config_data)

    return settings


# Global settings instance
settings = load_config()
