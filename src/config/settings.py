"""
Configuration management for the Solana Trading Bot.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class SolanaConfig(BaseModel):
    rpc_url: str = "https://api.mainnet-beta.solana.com"
    ws_url: str = "wss://api.mainnet-beta.solana.com"
    commitment: str = "confirmed"


class DatabaseConfig(BaseModel):
    url: str = "sqlite:///solana_trading_bot.db"
    echo: bool = False


class CopyTradingConfig(BaseModel):
    enabled: bool = True
    max_position_size_sol: float = 1.0
    min_position_size_sol: float = 0.01
    slippage_tolerance: float = 0.05


class RiskManagementConfig(BaseModel):
    stop_loss_percentage: float = 0.15
    take_profit_percentage: float = 2.0
    max_daily_loss_sol: float = 5.0
    max_concurrent_positions: int = 10


class PositionSizingConfig(BaseModel):
    base_size_sol: float = 0.1
    size_multiplier_by_confidence: Dict[str, float] = {
        "high": 2.0,
        "medium": 1.0,
        "low": 0.5
    }


class TradingConfig(BaseModel):
    copy_trading: CopyTradingConfig = CopyTradingConfig()
    risk_management: RiskManagementConfig = RiskManagementConfig()
    position_sizing: PositionSizingConfig = PositionSizingConfig()


class TraderClassificationConfig(BaseModel):
    sniper: Dict[str, float] = {
        "min_speed_score": 0.8,
        "min_roi": 5.0,
        "max_hold_time_hours": 24
    }
    insider: Dict[str, float] = {
        "min_early_entry_score": 0.9,
        "min_roi": 20.0,
        "unusual_timing_threshold": 0.8
    }
    whale: Dict[str, float] = {
        "min_position_size_sol": 10.0,
        "min_market_impact": 0.05
    }
    dev: Dict[str, float] = {
        "min_token_creation_correlation": 0.7,
        "min_roi": 50.0
    }


class WalletTrackingConfig(BaseModel):
    min_roi_threshold: float = 10.0
    min_win_rate: float = 0.6
    min_trades: int = 5
    classification: TraderClassificationConfig = TraderClassificationConfig()


class TransactionMonitoringConfig(BaseModel):
    enabled: bool = True
    dex_programs: list = [
        "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",  # Raydium
        "9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP",  # Orca
        "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",   # Jupiter
    ]


class PerformanceMonitoringConfig(BaseModel):
    update_interval_seconds: int = 300
    cleanup_old_data_days: int = 30


class MonitoringConfig(BaseModel):
    transaction_monitoring: TransactionMonitoringConfig = TransactionMonitoringConfig()
    performance_monitoring: PerformanceMonitoringConfig = PerformanceMonitoringConfig()


class APIConfig(BaseModel):
    jupiter: Dict[str, Any] = {
        "base_url": "https://quote-api.jup.ag/v6",
        "timeout": 10
    }
    birdeye: Dict[str, Any] = {
        "base_url": "https://public-api.birdeye.so",
        "timeout": 10
    }
    dexscreener: Dict[str, Any] = {
        "base_url": "https://api.dexscreener.com/latest",
        "timeout": 10
    }


class LoggingConfig(BaseModel):
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/trading_bot.log"
    max_file_size_mb: int = 100
    backup_count: int = 5


class SecurityConfig(BaseModel):
    private_key_env: str = "SOLANA_PRIVATE_KEY"
    encrypt_database: bool = False


class FeatureFlags(BaseModel):
    enable_copy_trading: bool = True
    enable_wallet_discovery: bool = True
    enable_performance_tracking: bool = True
    enable_risk_management: bool = True
    enable_notifications: bool = False


class Settings(BaseSettings):
    """Main settings class that loads configuration from YAML and environment variables."""
    
    solana: SolanaConfig = SolanaConfig()
    database: DatabaseConfig = DatabaseConfig()
    trading: TradingConfig = TradingConfig()
    wallet_tracking: WalletTrackingConfig = WalletTrackingConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    apis: APIConfig = APIConfig()
    logging: LoggingConfig = LoggingConfig()
    security: SecurityConfig = SecurityConfig()
    features: FeatureFlags = FeatureFlags()
    
    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"


def load_config(config_path: str = "config.yaml") -> Settings:
    """Load configuration from YAML file and environment variables."""
    
    # Load from YAML file if it exists
    config_data = {}
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f) or {}
    
    # Create settings instance with YAML data
    settings = Settings(**config_data)
    
    return settings


# Global settings instance
settings = load_config()
