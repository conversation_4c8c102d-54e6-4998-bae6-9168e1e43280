#!/usr/bin/env python3
"""
Test the fixed API configurations for the Solana Trading Bot.
"""

import asyncio
import aiohttp
import json
from datetime import datetime

# Test the fixed configurations
FIXED_ENDPOINTS = {
    # Jupiter API (Working)
    "jupiter_quote": "https://lite-api.jup.ag/swap/v1/quote",
    "jupiter_price": "https://lite-api.jup.ag/price/v2",
    "jupiter_tokens": "https://lite-api.jup.ag/tokens/v1/mints/tradable",
    
    # DexScreener API (Working)
    "dexscreener_search": "https://api.dexscreener.com/latest/dex/search",
    "dexscreener_pairs": "https://api.dexscreener.com/latest/dex/pairs/solana",
    
    # Pump.fun API (Fixed)
    "pumpfun_coins": "https://frontend-api-v3.pump.fun/coins",
    "pumpfun_latest": "https://frontend-api-v3.pump.fun/coins/latest",
    "pumpfun_sol_price": "https://frontend-api-v3.pump.fun/sol-price",
    
    # Helius RPC (Working)
    "helius_rpc": "https://mainnet.helius-rpc.com",
    "fallback_rpc": "https://api.mainnet-beta.solana.com",
}

async def test_fixed_apis():
    """Test all fixed API endpoints."""
    print("🔧 Testing Fixed API Configurations")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    results = {}
    
    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=10),
        headers={"User-Agent": "SolanaBot/1.0"}
    ) as session:
        
        # Test Jupiter APIs
        print("\n🚀 Testing Jupiter API (Fixed)...")
        
        # Jupiter Quote
        quote_params = {
            "inputMint": "So11111111111111111111111111111111111111112",
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "amount": "100000000",
            "slippageBps": "50"
        }
        
        try:
            async with session.get(FIXED_ENDPOINTS["jupiter_quote"], params=quote_params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ Jupiter Quote: Working - Route found with {len(data.get('routePlan', []))} steps")
                    results["jupiter_quote"] = "✅ Working"
                else:
                    print(f"  ❌ Jupiter Quote: Failed ({response.status})")
                    results["jupiter_quote"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 Jupiter Quote: Error - {e}")
            results["jupiter_quote"] = f"💥 Error - {e}"
        
        # Jupiter Price
        try:
            async with session.get(FIXED_ENDPOINTS["jupiter_price"], params={"ids": "So11111111111111111111111111111111111111112"}) as response:
                if response.status == 200:
                    data = await response.json()
                    sol_price = data.get("data", {}).get("So11111111111111111111111111111111111111112", {}).get("price")
                    print(f"  ✅ Jupiter Price: Working - SOL price: ${sol_price}")
                    results["jupiter_price"] = "✅ Working"
                else:
                    print(f"  ❌ Jupiter Price: Failed ({response.status})")
                    results["jupiter_price"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 Jupiter Price: Error - {e}")
            results["jupiter_price"] = f"💥 Error - {e}"
        
        # Test DexScreener APIs
        print("\n📊 Testing DexScreener API...")
        
        try:
            async with session.get(FIXED_ENDPOINTS["dexscreener_search"], params={"q": "BONK"}) as response:
                if response.status == 200:
                    data = await response.json()
                    pairs_count = len(data.get("pairs", []))
                    print(f"  ✅ DexScreener Search: Working - Found {pairs_count} BONK pairs")
                    results["dexscreener_search"] = "✅ Working"
                else:
                    print(f"  ❌ DexScreener Search: Failed ({response.status})")
                    results["dexscreener_search"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 DexScreener Search: Error - {e}")
            results["dexscreener_search"] = f"💥 Error - {e}"
        
        # Test Fixed Pump.fun APIs
        print("\n🚀 Testing Fixed Pump.fun API...")
        
        # Test primary endpoint with working params
        try:
            async with session.get(FIXED_ENDPOINTS["pumpfun_coins"], params={"offset": "0", "limit": "5"}) as response:
                if response.status == 200:
                    data = await response.json()
                    coins_count = len(data) if isinstance(data, list) else 1
                    print(f"  ✅ Pump.fun Coins (Fixed): Working - Retrieved {coins_count} coins")
                    results["pumpfun_coins"] = "✅ Working"
                else:
                    print(f"  ❌ Pump.fun Coins: Failed ({response.status})")
                    results["pumpfun_coins"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 Pump.fun Coins: Error - {e}")
            results["pumpfun_coins"] = f"💥 Error - {e}"
        
        # Test fallback endpoint
        try:
            async with session.get(FIXED_ENDPOINTS["pumpfun_latest"]) as response:
                if response.status == 200:
                    data = await response.json()
                    coin_name = data.get("name", "Unknown") if isinstance(data, dict) else "Unknown"
                    print(f"  ✅ Pump.fun Latest (Fallback): Working - Latest coin: {coin_name}")
                    results["pumpfun_latest"] = "✅ Working"
                else:
                    print(f"  ❌ Pump.fun Latest: Failed ({response.status})")
                    results["pumpfun_latest"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 Pump.fun Latest: Error - {e}")
            results["pumpfun_latest"] = f"💥 Error - {e}"
        
        # Test SOL price
        try:
            async with session.get(FIXED_ENDPOINTS["pumpfun_sol_price"]) as response:
                if response.status == 200:
                    data = await response.json()
                    sol_price = data.get("solPrice", "Unknown")
                    print(f"  ✅ Pump.fun SOL Price: Working - ${sol_price}")
                    results["pumpfun_sol_price"] = "✅ Working"
                else:
                    print(f"  ❌ Pump.fun SOL Price: Failed ({response.status})")
                    results["pumpfun_sol_price"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 Pump.fun SOL Price: Error - {e}")
            results["pumpfun_sol_price"] = f"💥 Error - {e}"
        
        # Test Helius RPC
        print("\n⚡ Testing Helius RPC...")
        
        rpc_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getHealth"
        }
        
        try:
            async with session.post(FIXED_ENDPOINTS["helius_rpc"], json=rpc_payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if "result" in data and data["result"] == "ok":
                        print(f"  ✅ Helius RPC: Working - Health check passed")
                        results["helius_rpc"] = "✅ Working"
                    else:
                        print(f"  ⚠️ Helius RPC: Partial - Response: {data}")
                        results["helius_rpc"] = "⚠️ Partial"
                else:
                    print(f"  ❌ Helius RPC: Failed ({response.status})")
                    results["helius_rpc"] = f"❌ Failed ({response.status})"
        except Exception as e:
            print(f"  💥 Helius RPC: Error - {e}")
            results["helius_rpc"] = f"💥 Error - {e}"
    
    # Print summary
    print("\n" + "="*60)
    print("📋 FIXED API TEST SUMMARY")
    print("="*60)
    
    working_count = sum(1 for status in results.values() if "✅" in status)
    total_count = len(results)
    
    for endpoint, status in results.items():
        print(f"{endpoint.replace('_', ' ').title()}: {status}")
    
    print(f"\n📊 Success Rate: {working_count}/{total_count} ({working_count/total_count*100:.1f}%)")
    
    if working_count == total_count:
        print("🎉 All fixed APIs are working perfectly!")
        print("✅ The bot is ready to run with these configurations!")
    elif working_count >= total_count * 0.8:
        print("✅ Most APIs are working - bot should function well!")
    else:
        print("⚠️ Some APIs still need attention")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return working_count == total_count

async def main():
    """Main test function."""
    success = await test_fixed_apis()
    
    if success:
        print("\n🚀 READY TO RUN THE BOT!")
        print("All API connections are working with the fixed configuration.")
    else:
        print("\n⚠️ Some issues remain, but the bot should still work with available APIs.")

if __name__ == "__main__":
    asyncio.run(main())
