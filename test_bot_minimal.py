#!/usr/bin/env python3
"""
Minimal test run of the Solana Trading Bot without Telegram functionality.
"""

import asyncio
import sys
from datetime import datetime

# Import core components
from src.config.settings import settings
from src.data.database import db_manager
from src.blockchain.pumpfun_monitor import pumpfun_monitor
from src.utils.logger import get_logger

# Set up logger
logger = get_logger("test_bot")


async def test_core_functionality():
    """Test core bot functionality without Telegram."""
    logger.info("🚀 Starting Minimal Solana Trading Bot Test...")
    logger.info("=" * 60)

    try:
        # Test 1: Database initialization
        logger.info("📊 Testing database connection...")
        await db_manager.initialize()
        logger.info("✅ Database initialized successfully")

        # Test 2: Configuration validation
        logger.info("⚙️ Testing configuration...")
        logger.info(f"RPC URL: {settings.solana.rpc_url}")
        logger.info(
            f"Pump.fun monitoring: {settings.monitoring.pumpfun_monitoring.enabled}"
        )
        logger.info(f"Copy trading: {settings.features.enable_copy_trading}")
        logger.info(f"Telegram bot: {settings.features.enable_telegram_bot}")
        logger.info("✅ Configuration loaded successfully")

        # Test 3: Pump.fun API test
        logger.info("🚀 Testing Pump.fun API connection...")
        try:
            # Initialize pump.fun monitor
            await pumpfun_monitor.initialize()
            logger.info("✅ Pump.fun monitor initialized")

            # Test API call
            latest_tokens = await pumpfun_monitor.get_latest_tokens(limit=3)
            if latest_tokens:
                logger.info(
                    f"✅ Retrieved {len(latest_tokens)} latest tokens from Pump.fun"
                )
                for i, token in enumerate(latest_tokens[:2], 1):
                    logger.info(
                        f"  {i}. {token.get('name', 'Unknown')} ({token.get('symbol', 'Unknown')})"
                    )
            else:
                logger.warning("⚠️ No tokens retrieved from Pump.fun API")

        except Exception as e:
            logger.error(f"❌ Pump.fun API test failed: {e}")

        # Test 4: Monitor for a short period
        logger.info("⏱️ Running monitoring test for 30 seconds...")

        # Start monitoring task
        monitor_task = asyncio.create_task(pumpfun_monitor.start_monitoring())

        # Let it run for 30 seconds
        await asyncio.sleep(30)

        # Stop monitoring
        monitor_task.cancel()
        await pumpfun_monitor.stop_monitoring()

        logger.info("✅ Monitoring test completed")

        # Test 5: Database cleanup
        logger.info("🧹 Cleaning up...")
        await db_manager.close()
        logger.info("✅ Database connections closed")

        logger.info("=" * 60)
        logger.info("🎉 All core functionality tests passed!")
        logger.info("✅ The Solana Trading Bot core is working correctly")

        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        success = await test_core_functionality()

        if success:
            logger.info("\n🚀 CORE FUNCTIONALITY TEST: PASSED")
            logger.info("The bot's core systems are working correctly!")
            logger.info("You can now run the full bot with: python run_bot.py")
        else:
            logger.error("\n❌ CORE FUNCTIONALITY TEST: FAILED")
            logger.error(
                "Please check the errors above and fix them before running the full bot."
            )
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test crashed: {e}")
        sys.exit(1)
    finally:
        logger.info(
            f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )


if __name__ == "__main__":
    asyncio.run(main())
