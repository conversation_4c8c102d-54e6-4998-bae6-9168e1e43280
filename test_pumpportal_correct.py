#!/usr/bin/env python3
"""
Test PumpPortal WebSocket API with correct subscription methods.
"""

import asyncio
import json
import websockets
from datetime import datetime

PUMPPORTAL_WS_URL = "wss://pumpportal.fun/api/data"

async def test_pumpportal_correct_subscriptions():
    """Test PumpPortal with the correct subscription methods."""
    print("🔌 Testing PumpPortal WebSocket with Correct Methods...")
    print(f"URL: {PUMPPORTAL_WS_URL}")
    print("="*60)
    
    try:
        async with websockets.connect(
            PUMPPORTAL_WS_URL,
            ping_interval=20,
            ping_timeout=10,
            close_timeout=10
        ) as websocket:
            print("✅ WebSocket connection established!")
            
            # Use the correct subscription methods from the error message
            correct_subscriptions = [
                {"method": "subscribeNewToken"},
                {"method": "subscribeTokenTrade"},
                {"method": "subscribeAccountTrade"},
                {"method": "subscribeMigration"},
            ]
            
            # Test each subscription method
            for i, sub_msg in enumerate(correct_subscriptions, 1):
                try:
                    print(f"\n📤 Sending subscription {i}: {sub_msg}")
                    await websocket.send(json.dumps(sub_msg))
                    
                    # Wait for response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        print(f"📥 Response {i}: {response}")
                        
                        try:
                            data = json.loads(response)
                            if "errors" in data:
                                print(f"❌ Error: {data['errors']}")
                            else:
                                print(f"✅ Success: {data}")
                        except json.JSONDecodeError:
                            print("⚠️ Non-JSON response")
                            
                    except asyncio.TimeoutError:
                        print(f"⏰ No immediate response to subscription {i}")
                        
                except Exception as e:
                    print(f"❌ Error with subscription {i}: {e}")
            
            # Listen for real-time data for 45 seconds
            print(f"\n👂 Listening for real-time data for 45 seconds...")
            start_time = asyncio.get_event_loop().time()
            message_count = 0
            token_data = []
            trade_data = []
            
            while asyncio.get_event_loop().time() - start_time < 45:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    message_count += 1
                    
                    print(f"\n📨 Message {message_count}:")
                    print(f"   Raw: {message[:200]}...")
                    
                    try:
                        data = json.loads(message)
                        print(f"   📊 JSON keys: {list(data.keys()) if isinstance(data, dict) else 'array'}")
                        
                        # Analyze the data structure
                        if isinstance(data, dict):
                            # Check for new token data
                            if any(key in data for key in ['mint', 'name', 'symbol', 'uri', 'creator']):
                                print(f"   🪙 NEW TOKEN detected!")
                                token_data.append(data)
                                if 'name' in data:
                                    print(f"      Name: {data.get('name', 'Unknown')}")
                                if 'symbol' in data:
                                    print(f"      Symbol: {data.get('symbol', 'Unknown')}")
                                if 'mint' in data:
                                    print(f"      Mint: {data.get('mint', 'Unknown')}")
                            
                            # Check for trade data
                            if any(key in data for key in ['signature', 'sol_amount', 'token_amount', 'is_buy']):
                                print(f"   💰 TRADE detected!")
                                trade_data.append(data)
                                if 'is_buy' in data:
                                    action = "BUY" if data.get('is_buy') else "SELL"
                                    print(f"      Action: {action}")
                                if 'sol_amount' in data:
                                    print(f"      SOL Amount: {data.get('sol_amount', 0)}")
                                if 'mint' in data:
                                    print(f"      Token: {data.get('mint', 'Unknown')}")
                        
                    except json.JSONDecodeError:
                        print(f"   ⚠️ Non-JSON message")
                        
                    # Stop after 15 messages to avoid spam
                    if message_count >= 15:
                        print(f"\n📊 Received {message_count} messages, stopping early...")
                        break
                        
                except asyncio.TimeoutError:
                    print("⏰ No messages in 5 seconds...")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("🔌 WebSocket connection closed")
                    break
            
            # Summary
            print(f"\n" + "="*60)
            print(f"📊 PUMPPORTAL DATA SUMMARY")
            print(f"="*60)
            print(f"Total messages received: {message_count}")
            print(f"New tokens detected: {len(token_data)}")
            print(f"Trades detected: {len(trade_data)}")
            
            if token_data:
                print(f"\n🪙 Sample Token Data:")
                for i, token in enumerate(token_data[:3], 1):
                    print(f"  {i}. {token.get('name', 'Unknown')} ({token.get('symbol', 'Unknown')})")
            
            if trade_data:
                print(f"\n💰 Sample Trade Data:")
                for i, trade in enumerate(trade_data[:3], 1):
                    action = "BUY" if trade.get('is_buy') else "SELL"
                    amount = trade.get('sol_amount', 0)
                    print(f"  {i}. {action} - {amount} SOL")
            
            if message_count > 0:
                print(f"\n✅ PumpPortal WebSocket is FUNCTIONAL!")
                print(f"🎯 Real-time data is flowing correctly")
                return True
            else:
                print(f"\n⚠️ No data received - may need different subscription parameters")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

async def test_subscription_with_parameters():
    """Test subscriptions with parameters."""
    print(f"\n🔧 Testing Subscriptions with Parameters...")
    
    try:
        async with websockets.connect(PUMPPORTAL_WS_URL) as websocket:
            
            # Try subscriptions with parameters
            param_subscriptions = [
                {"method": "subscribeNewToken", "keys": ["all"]},
                {"method": "subscribeTokenTrade", "keys": ["all"]},
                {"method": "subscribeNewToken", "params": {}},
                {"method": "subscribeTokenTrade", "params": {}},
            ]
            
            for i, sub_msg in enumerate(param_subscriptions, 1):
                try:
                    print(f"\n📤 Testing subscription with params {i}: {sub_msg}")
                    await websocket.send(json.dumps(sub_msg))
                    
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        print(f"📥 Response: {response}")
                        
                        data = json.loads(response)
                        if "errors" not in data:
                            print(f"✅ Success with parameters!")
                            
                            # Listen for a few messages
                            for j in range(3):
                                try:
                                    msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                                    print(f"📨 Data message {j+1}: {msg[:150]}...")
                                except asyncio.TimeoutError:
                                    break
                            return True
                        else:
                            print(f"❌ Error: {data['errors']}")
                            
                    except asyncio.TimeoutError:
                        print(f"⏰ No response")
                        
                except Exception as e:
                    print(f"❌ Error: {e}")
                    
    except Exception as e:
        print(f"❌ Parameter test failed: {e}")
        
    return False

async def main():
    """Main test function."""
    print("🔍 PumpPortal Correct API Test")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # Test with correct subscription methods
    basic_working = await test_pumpportal_correct_subscriptions()
    
    # Test with parameters if basic didn't work
    if not basic_working:
        param_working = await test_subscription_with_parameters()
    else:
        param_working = True
    
    print(f"\n" + "="*60)
    print(f"🎯 FINAL ASSESSMENT")
    print(f"="*60)
    
    if basic_working or param_working:
        print(f"✅ PumpPortal WebSocket API: FUNCTIONAL")
        print(f"🚀 The new API URL in your config is working!")
        print(f"📝 Correct subscription methods:")
        print(f"   • subscribeNewToken - for new token launches")
        print(f"   • subscribeTokenTrade - for token trades")
        print(f"   • subscribeAccountTrade - for account trades")
        print(f"   • subscribeMigration - for token migrations")
        print(f"\n🔧 UPDATE NEEDED: Update your pump.fun monitor to use these methods")
    else:
        print(f"❌ PumpPortal WebSocket API: NOT FUNCTIONAL")
        print(f"🔄 May need API key or different approach")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return basic_working or param_working

if __name__ == "__main__":
    asyncio.run(main())
