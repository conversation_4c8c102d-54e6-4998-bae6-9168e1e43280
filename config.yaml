# Solana Trading Bot Configuration

# Solana RPC Configuration
solana:
  rpc_url: "https://api.mainnet-beta.solana.com"
  ws_url: "wss://api.mainnet-beta.solana.com"
  commitment: "confirmed"

# Database Configuration
database:
  url: "sqlite:///solana_trading_bot.db"
  echo: false

# Trading Configuration
trading:
  # Copy trading settings
  copy_trading:
    enabled: true
    max_position_size_sol: 1.0
    min_position_size_sol: 0.01
    slippage_tolerance: 0.05 # 5%

  # Risk management
  risk_management:
    stop_loss_percentage: 0.15 # 15% stop loss
    take_profit_percentage: 2.0 # 200% take profit
    max_daily_loss_sol: 5.0
    max_concurrent_positions: 10

  # Position sizing
  position_sizing:
    base_size_sol: 0.1
    size_multiplier_by_confidence:
      high: 2.0
      medium: 1.0
      low: 0.5

# Wallet Tracking Configuration
wallet_tracking:
  # Minimum performance thresholds
  min_roi_threshold: 10.0 # 10x minimum ROI
  min_win_rate: 0.6 # 60% win rate
  min_trades: 5 # Minimum number of trades to analyze

  # Trader classification thresholds
  classification:
    sniper:
      min_speed_score: 0.8 # Fast entry timing
      min_roi: 5.0
      max_hold_time_hours: 24
    insider:
      min_early_entry_score: 0.9
      min_roi: 20.0
      unusual_timing_threshold: 0.8
    whale:
      min_position_size_sol: 10.0
      min_market_impact: 0.05
    dev:
      min_token_creation_correlation: 0.7
      min_roi: 50.0

# Monitoring Configuration
monitoring:
  # Transaction monitoring
  transaction_monitoring:
    enabled: true
    dex_programs:
      - "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" # Raydium
      - "9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP" # Orca
      - "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4" # Jupiter
      - "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" # Pump.fun

  # Pump.fun specific monitoring
  pumpfun_monitoring:
    enabled: true
    api_base_url: "https://frontend-api.pump.fun"
    new_token_check_interval: 5 # seconds
    trending_check_interval: 30 # seconds
    snipe_time_threshold: 60 # seconds to qualify as snipe
    early_trade_threshold: 300 # seconds to qualify as early trade
    monitor_duration_hours: 1 # how long to monitor each token after launch

  # Performance monitoring
  performance_monitoring:
    update_interval_seconds: 300 # 5 minutes
    cleanup_old_data_days: 30

# API Configuration
apis:
  jupiter:
    base_url: "https://quote-api.jup.ag/v6"
    timeout: 10

  birdeye:
    base_url: "https://public-api.birdeye.so"
    timeout: 10

  dexscreener:
    base_url: "https://api.dexscreener.com/latest"
    timeout: 10

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/trading_bot.log"
  max_file_size_mb: 100
  backup_count: 5

# Security Configuration
security:
  private_key_env: "SOLANA_PRIVATE_KEY"
  encrypt_database: false

# Telegram Configuration
telegram:
  bot_token: "**********************************************"
  authorized_users: [1484472507] # List of authorized Telegram user IDs
  notifications:
    enabled: true
    rate_limits:
      trade_notifications: 0 # No cooldown for trades
      sniper_alerts: 300 # 5 minutes
      pump_launches: 10 # 10 seconds
      risk_alerts: 60 # 1 minute
  trading:
    enabled: true
    require_confirmation: true
    max_manual_trade_size: 1.0 # SOL

# Feature Flags
features:
  enable_copy_trading: true
  enable_wallet_discovery: true
  enable_performance_tracking: true
  enable_risk_management: true
  enable_notifications: true
  enable_telegram_bot: true
