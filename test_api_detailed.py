#!/usr/bin/env python3
"""
Detailed API testing with error analysis for the Solana Trading Bot.
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_birdeye_detailed():
    """Test Birdeye API with detailed error reporting."""
    print("🐦 Detailed Birdeye API Testing...")
    
    urls = [
        ("Price API", "https://public-api.birdeye.so/defi/price", {"address": "So11111111111111111111111111111111111111112", "chain": "solana"}),
        ("Multi Price", "https://public-api.birdeye.so/defi/multi_price", {"list_address": "So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "chain": "solana"}),
        ("Token Overview", "https://public-api.birdeye.so/defi/token_overview", {"address": "So11111111111111111111111111111111111111112"}),
    ]
    
    async with aiohttp.ClientSession() as session:
        for name, url, params in urls:
            try:
                print(f"\n  Testing {name}:")
                print(f"    URL: {url}")
                print(f"    Params: {params}")
                
                async with session.get(url, params=params) as response:
                    print(f"    Status: {response.status}")
                    print(f"    Headers: {dict(response.headers)}")
                    
                    text = await response.text()
                    print(f"    Response: {text[:500]}...")
                    
                    if response.status == 200:
                        try:
                            data = json.loads(text)
                            print(f"    ✅ Success - Data keys: {list(data.keys()) if isinstance(data, dict) else 'array'}")
                        except json.JSONDecodeError:
                            print(f"    ⚠️ Non-JSON response")
                    else:
                        print(f"    ❌ Failed with status {response.status}")
                        
            except Exception as e:
                print(f"    💥 Error: {e}")

async def test_pumpfun_detailed():
    """Test Pump.fun API with detailed error reporting."""
    print("\n🚀 Detailed Pump.fun API Testing...")
    
    urls = [
        ("Latest Coins", "https://frontend-api-v3.pump.fun/coins/latest", None),
        ("Live Coins", "https://frontend-api-v3.pump.fun/coins/currently-live", None),
        ("Featured Coins", "https://frontend-api-v3.pump.fun/coins/featured/1h", None),
        ("SOL Price", "https://frontend-api-v3.pump.fun/sol-price", None),
        ("V2 Latest", "https://frontend-api-v2.pump.fun/coins/latest", None),
        ("V1 Latest", "https://frontend-api.pump.fun/coins/latest", None),
    ]
    
    async with aiohttp.ClientSession() as session:
        for name, url, params in urls:
            try:
                print(f"\n  Testing {name}:")
                print(f"    URL: {url}")
                
                async with session.get(url, params=params) as response:
                    print(f"    Status: {response.status}")
                    
                    text = await response.text()
                    print(f"    Response: {text[:300]}...")
                    
                    if response.status == 200:
                        try:
                            data = json.loads(text)
                            if isinstance(data, list):
                                print(f"    ✅ Success - Array with {len(data)} items")
                            else:
                                print(f"    ✅ Success - Data keys: {list(data.keys())}")
                        except json.JSONDecodeError:
                            print(f"    ⚠️ Non-JSON response")
                    else:
                        print(f"    ❌ Failed with status {response.status}")
                        
            except Exception as e:
                print(f"    💥 Error: {e}")

async def test_jupiter_detailed():
    """Test Jupiter API with detailed error reporting."""
    print("\n🚀 Detailed Jupiter API Testing...")
    
    # Test both old and new endpoints
    tests = [
        ("New Quote API", "https://lite-api.jup.ag/swap/v1/quote", {
            "inputMint": "So11111111111111111111111111111111111111112",
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "amount": "100000000",
            "slippageBps": "50"
        }),
        ("Legacy Quote API", "https://quote-api.jup.ag/v6/quote", {
            "inputMint": "So11111111111111111111111111111111111111112",
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "amount": "100000000",
            "slippageBps": "50"
        }),
        ("New Price API", "https://lite-api.jup.ag/price/v2", {"ids": "So11111111111111111111111111111111111111112"}),
        ("Legacy Price API", "https://price.jup.ag/v6", {"ids": "So11111111111111111111111111111111111111112"}),
    ]
    
    async with aiohttp.ClientSession() as session:
        for name, url, params in tests:
            try:
                print(f"\n  Testing {name}:")
                print(f"    URL: {url}")
                
                async with session.get(url, params=params) as response:
                    print(f"    Status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print(f"    ✅ Success - Data keys: {list(data.keys()) if isinstance(data, dict) else 'array'}")
                    else:
                        text = await response.text()
                        print(f"    ❌ Failed: {text[:200]}...")
                        
            except Exception as e:
                print(f"    💥 Error: {e}")

async def test_rate_limits():
    """Test API rate limits."""
    print("\n⏱️ Testing Rate Limits...")
    
    # Test multiple rapid requests to see rate limiting
    async with aiohttp.ClientSession() as session:
        print("  Making 5 rapid requests to Jupiter...")
        for i in range(5):
            try:
                start_time = asyncio.get_event_loop().time()
                async with session.get("https://lite-api.jup.ag/price/v2", params={"ids": "So11111111111111111111111111111111111111112"}) as response:
                    end_time = asyncio.get_event_loop().time()
                    print(f"    Request {i+1}: {response.status} ({end_time-start_time:.2f}s)")
                    
                    if response.status == 429:
                        print(f"    ⚠️ Rate limited!")
                        headers = dict(response.headers)
                        print(f"    Rate limit headers: {headers}")
                        
            except Exception as e:
                print(f"    💥 Error on request {i+1}: {e}")

async def test_websocket_connections():
    """Test WebSocket connections."""
    print("\n🔌 Testing WebSocket Connections...")
    
    try:
        import websockets
        
        # Test Helius WebSocket
        print("  Testing Helius WebSocket...")
        try:
            async with websockets.connect("wss://mainnet.helius-rpc.com", ping_interval=20, ping_timeout=10) as websocket:
                # Send a test subscription
                test_request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                await websocket.send(json.dumps(test_request))
                
                # Wait for response with timeout
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"    ✅ WebSocket connected and responsive")
                print(f"    Response: {response[:100]}...")
                
        except asyncio.TimeoutError:
            print("    ⏰ WebSocket connection timed out")
        except Exception as e:
            print(f"    ❌ WebSocket error: {e}")
            
    except ImportError:
        print("    ⚠️ websockets library not installed")

async def main():
    """Run detailed API tests."""
    print("🔍 Detailed API Connection Analysis")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    await test_jupiter_detailed()
    await test_birdeye_detailed()
    await test_pumpfun_detailed()
    await test_rate_limits()
    await test_websocket_connections()
    
    print("\n" + "="*60)
    print("✅ Detailed API analysis completed!")
    print(f"⏰ Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
