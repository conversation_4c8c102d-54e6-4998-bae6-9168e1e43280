2025-05-25 17:54:55,737 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:55.736854Z"}
2025-05-25 17:54:56,397 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:54:56.397454Z"}
2025-05-25 17:54:56,403 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.403735Z"}
2025-05-25 17:54:56,413 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.412323Z"}
2025-05-25 17:54:56,447 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:54:56.447070Z"}
2025-05-25 17:54:56,487 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:54:56.487287Z"}
2025-05-25 17:54:56,498 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:54:56.498359Z"}
2025-05-25 17:54:56,516 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:54:56.516584Z"}
2025-05-25 17:54:56,528 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:54:56.528819Z"}
2025-05-25 17:54:56,537 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.537572Z"}
2025-05-25 17:54:56,543 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:54:56.543847Z"}
2025-05-25 17:54:56,550 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.550749Z"}
2025-05-25 17:54:56,560 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.560229Z"}
2025-05-25 17:54:56,566 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:54:56.566354Z"}
2025-05-25 17:54:56,573 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.572971Z"}
2025-05-25 17:56:10,941 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:10.941571Z"}
2025-05-25 17:56:11,118 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:56:11.118560Z"}
2025-05-25 17:56:11,124 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.124063Z"}
2025-05-25 17:56:11,129 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.129341Z"}
2025-05-25 17:56:11,135 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:56:11.135574Z"}
2025-05-25 17:56:11,150 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:56:11.150751Z"}
2025-05-25 17:56:11,191 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:56:11.191162Z"}
2025-05-25 17:56:11,219 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:56:11.219027Z"}
2025-05-25 17:56:11,235 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:56:11.235235Z"}
2025-05-25 17:56:11,248 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.248662Z"}
2025-05-25 17:56:11,276 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:56:11.276372Z"}
2025-05-25 17:56:11,289 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.289307Z"}
2025-05-25 17:56:11,301 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.301738Z"}
2025-05-25 17:56:11,311 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:56:11.311540Z"}
2025-05-25 17:56:11,315 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.315073Z"}
2025-05-25 17:57:35,285 - test_bot - INFO - {"event": "\u23f0 Test started at: 2025-05-25 17:57:35", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.285544Z"}
2025-05-25 17:57:35,294 - test_bot - INFO - {"event": "\ud83d\ude80 Starting Minimal Solana Trading Bot Test...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.293997Z"}
2025-05-25 17:57:35,298 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.298890Z"}
2025-05-25 17:57:35,318 - test_bot - INFO - {"event": "\ud83d\udcca Testing database connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.318141Z"}
2025-05-25 17:57:35,423 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:57:35.423852Z"}
2025-05-25 17:57:35,426 - test_bot - INFO - {"event": "\u2705 Database initialized successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.426162Z"}
2025-05-25 17:57:35,428 - test_bot - INFO - {"event": "\u2699\ufe0f Testing configuration...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.428409Z"}
2025-05-25 17:57:35,439 - test_bot - INFO - {"event": "RPC URL: https://api.mainnet-beta.solana.com", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.439508Z"}
2025-05-25 17:57:35,441 - test_bot - INFO - {"event": "Pump.fun monitoring: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.441886Z"}
2025-05-25 17:57:35,444 - test_bot - INFO - {"event": "Copy trading: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.444079Z"}
2025-05-25 17:57:35,476 - test_bot - INFO - {"event": "Telegram bot: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.476631Z"}
2025-05-25 17:57:35,479 - test_bot - INFO - {"event": "\u2705 Configuration loaded successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.478979Z"}
2025-05-25 17:57:35,489 - test_bot - INFO - {"event": "\ud83d\ude80 Testing Pump.fun API connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.489327Z"}
2025-05-25 17:57:35,493 - test_bot - ERROR - {"event": "\u274c Pump.fun API test failed: 'PumpFunMonitor' object has no attribute 'initialize'", "logger": "test_bot", "level": "error", "timestamp": "2025-05-25T21:57:35.493535Z"}
2025-05-25 17:57:35,510 - test_bot - INFO - {"event": "\u23f1\ufe0f Running monitoring test for 30 seconds...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.509941Z"}
2025-05-25 17:57:35,519 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:57:35.519199Z"}
2025-05-25 17:57:35,670 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:35.669959Z"}
2025-05-25 17:57:40,754 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:40.754005Z"}
2025-05-25 17:57:45,838 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:45.838399Z"}
2025-05-25 17:57:50,923 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:50.923363Z"}
2025-05-25 17:57:56,042 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:56.042403Z"}
2025-05-25 17:58:01,133 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:58:01.133000Z"}
2025-05-25 17:58:05,521 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.521289Z"}
2025-05-25 17:58:05,524 - test_bot - INFO - {"event": "\u2705 Monitoring test completed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.523943Z"}
2025-05-25 17:58:05,526 - test_bot - INFO - {"event": "\ud83e\uddf9 Cleaning up...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.526420Z"}
2025-05-25 17:58:05,541 - test_bot - INFO - {"event": "\u2705 Database connections closed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.541837Z"}
2025-05-25 17:58:05,555 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.555592Z"}
2025-05-25 17:58:05,557 - test_bot - INFO - {"event": "\ud83c\udf89 All core functionality tests passed!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.557839Z"}
2025-05-25 17:58:05,568 - test_bot - INFO - {"event": "\u2705 The Solana Trading Bot core is working correctly", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.563329Z"}
2025-05-25 17:58:05,577 - test_bot - INFO - {"event": "\n\ud83d\ude80 CORE FUNCTIONALITY TEST: PASSED", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.577430Z"}
2025-05-25 17:58:05,591 - test_bot - INFO - {"event": "The bot's core systems are working correctly!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.591562Z"}
2025-05-25 17:58:05,623 - test_bot - INFO - {"event": "You can now run the full bot with: python run_bot.py", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.623162Z"}
2025-05-25 17:58:05,625 - test_bot - INFO - {"event": "\u23f0 Test completed at: 2025-05-25 17:58:05", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.625613Z"}
2025-05-25 17:58:05,640 - PumpFunMonitor - INFO - {"event": "New token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.640499Z"}
2025-05-25 17:58:05,652 - PumpFunMonitor - INFO - {"event": "Trending token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.652611Z"}
2025-05-25 17:58:05,658 - PumpFunMonitor - INFO - {"event": "Cleanup task cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.658466Z"}
