2025-05-25 17:54:55,737 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:55.736854Z"}
2025-05-25 17:54:56,397 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:54:56.397454Z"}
2025-05-25 17:54:56,403 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.403735Z"}
2025-05-25 17:54:56,413 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.412323Z"}
2025-05-25 17:54:56,447 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:54:56.447070Z"}
2025-05-25 17:54:56,487 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:54:56.487287Z"}
2025-05-25 17:54:56,498 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:54:56.498359Z"}
2025-05-25 17:54:56,516 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:54:56.516584Z"}
2025-05-25 17:54:56,528 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:54:56.528819Z"}
2025-05-25 17:54:56,537 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.537572Z"}
2025-05-25 17:54:56,543 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:54:56.543847Z"}
2025-05-25 17:54:56,550 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.550749Z"}
2025-05-25 17:54:56,560 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.560229Z"}
2025-05-25 17:54:56,566 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:54:56.566354Z"}
2025-05-25 17:54:56,573 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.572971Z"}
2025-05-25 17:56:10,941 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:10.941571Z"}
2025-05-25 17:56:11,118 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:56:11.118560Z"}
2025-05-25 17:56:11,124 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.124063Z"}
2025-05-25 17:56:11,129 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.129341Z"}
2025-05-25 17:56:11,135 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:56:11.135574Z"}
2025-05-25 17:56:11,150 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:56:11.150751Z"}
2025-05-25 17:56:11,191 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:56:11.191162Z"}
2025-05-25 17:56:11,219 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:56:11.219027Z"}
2025-05-25 17:56:11,235 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:56:11.235235Z"}
2025-05-25 17:56:11,248 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.248662Z"}
2025-05-25 17:56:11,276 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:56:11.276372Z"}
2025-05-25 17:56:11,289 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.289307Z"}
2025-05-25 17:56:11,301 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.301738Z"}
2025-05-25 17:56:11,311 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:56:11.311540Z"}
2025-05-25 17:56:11,315 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.315073Z"}
2025-05-25 17:57:35,285 - test_bot - INFO - {"event": "\u23f0 Test started at: 2025-05-25 17:57:35", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.285544Z"}
2025-05-25 17:57:35,294 - test_bot - INFO - {"event": "\ud83d\ude80 Starting Minimal Solana Trading Bot Test...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.293997Z"}
2025-05-25 17:57:35,298 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.298890Z"}
2025-05-25 17:57:35,318 - test_bot - INFO - {"event": "\ud83d\udcca Testing database connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.318141Z"}
2025-05-25 17:57:35,423 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:57:35.423852Z"}
2025-05-25 17:57:35,426 - test_bot - INFO - {"event": "\u2705 Database initialized successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.426162Z"}
2025-05-25 17:57:35,428 - test_bot - INFO - {"event": "\u2699\ufe0f Testing configuration...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.428409Z"}
2025-05-25 17:57:35,439 - test_bot - INFO - {"event": "RPC URL: https://api.mainnet-beta.solana.com", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.439508Z"}
2025-05-25 17:57:35,441 - test_bot - INFO - {"event": "Pump.fun monitoring: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.441886Z"}
2025-05-25 17:57:35,444 - test_bot - INFO - {"event": "Copy trading: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.444079Z"}
2025-05-25 17:57:35,476 - test_bot - INFO - {"event": "Telegram bot: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.476631Z"}
2025-05-25 17:57:35,479 - test_bot - INFO - {"event": "\u2705 Configuration loaded successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.478979Z"}
2025-05-25 17:57:35,489 - test_bot - INFO - {"event": "\ud83d\ude80 Testing Pump.fun API connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.489327Z"}
2025-05-25 17:57:35,493 - test_bot - ERROR - {"event": "\u274c Pump.fun API test failed: 'PumpFunMonitor' object has no attribute 'initialize'", "logger": "test_bot", "level": "error", "timestamp": "2025-05-25T21:57:35.493535Z"}
2025-05-25 17:57:35,510 - test_bot - INFO - {"event": "\u23f1\ufe0f Running monitoring test for 30 seconds...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.509941Z"}
2025-05-25 17:57:35,519 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:57:35.519199Z"}
2025-05-25 17:57:35,670 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:35.669959Z"}
2025-05-25 17:57:40,754 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:40.754005Z"}
2025-05-25 17:57:45,838 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:45.838399Z"}
2025-05-25 17:57:50,923 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:50.923363Z"}
2025-05-25 17:57:56,042 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:56.042403Z"}
2025-05-25 17:58:01,133 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:58:01.133000Z"}
2025-05-25 17:58:05,521 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.521289Z"}
2025-05-25 17:58:05,524 - test_bot - INFO - {"event": "\u2705 Monitoring test completed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.523943Z"}
2025-05-25 17:58:05,526 - test_bot - INFO - {"event": "\ud83e\uddf9 Cleaning up...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.526420Z"}
2025-05-25 17:58:05,541 - test_bot - INFO - {"event": "\u2705 Database connections closed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.541837Z"}
2025-05-25 17:58:05,555 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.555592Z"}
2025-05-25 17:58:05,557 - test_bot - INFO - {"event": "\ud83c\udf89 All core functionality tests passed!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.557839Z"}
2025-05-25 17:58:05,568 - test_bot - INFO - {"event": "\u2705 The Solana Trading Bot core is working correctly", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.563329Z"}
2025-05-25 17:58:05,577 - test_bot - INFO - {"event": "\n\ud83d\ude80 CORE FUNCTIONALITY TEST: PASSED", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.577430Z"}
2025-05-25 17:58:05,591 - test_bot - INFO - {"event": "The bot's core systems are working correctly!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.591562Z"}
2025-05-25 17:58:05,623 - test_bot - INFO - {"event": "You can now run the full bot with: python run_bot.py", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.623162Z"}
2025-05-25 17:58:05,625 - test_bot - INFO - {"event": "\u23f0 Test completed at: 2025-05-25 17:58:05", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.625613Z"}
2025-05-25 17:58:05,640 - PumpFunMonitor - INFO - {"event": "New token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.640499Z"}
2025-05-25 17:58:05,652 - PumpFunMonitor - INFO - {"event": "Trending token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.652611Z"}
2025-05-25 17:58:05,658 - PumpFunMonitor - INFO - {"event": "Cleanup task cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.658466Z"}
2025-05-25 18:46:31,456 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.456573Z"}
2025-05-25 18:46:31,622 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:46:31.622346Z"}
2025-05-25 18:46:31,625 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.625427Z"}
2025-05-25 18:46:31,627 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.627806Z"}
2025-05-25 18:46:31,630 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:46:31.630520Z"}
2025-05-25 18:46:31,655 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T22:46:31.655722Z"}
2025-05-25 18:46:31,663 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T22:46:31.663001Z"}
2025-05-25 18:46:31,742 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T22:46:31.741884Z"}
2025-05-25 18:46:31,774 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T22:46:31.763601Z"}
2025-05-25 18:46:31,781 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.781163Z"}
2025-05-25 18:46:31,820 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T22:46:31.820381Z"}
2025-05-25 18:46:31,869 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.869355Z"}
2025-05-25 18:46:31,894 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.894049Z"}
2025-05-25 18:46:31,905 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T22:46:31.905015Z"}
2025-05-25 18:46:31,935 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.935541Z"}
2025-05-25 18:47:38,973 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.973327Z"}
2025-05-25 18:47:38,993 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.993781Z"}
2025-05-25 18:47:39,023 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.023435Z"}
2025-05-25 18:47:39,145 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:47:39.145619Z"}
2025-05-25 18:47:39,158 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.158521Z"}
2025-05-25 18:47:39,163 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.163394Z"}
2025-05-25 18:47:39,177 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:47:39.177044Z"}
2025-05-25 18:47:39,209 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.209601Z"}
2025-05-25 18:47:39,228 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.228144Z"}
2025-05-25 18:47:39,288 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.288079Z"}
2025-05-25 18:47:39,296 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.296179Z"}
2025-05-25 18:47:39,326 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.326022Z"}
2025-05-25 18:47:39,391 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.380255Z"}
2025-05-25 18:47:39,613 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:47:39.613500Z"}
2025-05-25 18:47:39,623 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:47:39.623665Z"}
2025-05-25 18:47:39,628 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:47:39.628676Z"}
2025-05-25 18:47:39,639 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.639707Z"}
2025-05-25 18:47:39,647 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.646932Z"}
2025-05-25 18:47:39,658 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.658237Z"}
2025-05-25 18:47:39,665 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.665154Z"}
2025-05-25 18:47:39,843 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:39.843811Z"}
2025-05-25 18:47:44,944 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:44.944444Z"}
2025-05-25 18:47:50,051 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:50.051812Z"}
2025-05-25 18:47:55,321 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:55.318947Z"}
2025-05-25 18:48:00,435 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:00.435812Z"}
2025-05-25 18:48:05,537 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:05.537077Z"}
2025-05-25 18:48:10,628 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:10.628524Z"}
2025-05-25 18:48:15,726 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:15.726263Z"}
2025-05-25 18:48:20,812 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:20.812546Z"}
2025-05-25 18:48:25,913 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:25.913146Z"}
2025-05-25 18:48:31,005 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:31.005795Z"}
2025-05-25 18:48:36,241 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:36.241753Z"}
2025-05-25 18:48:41,354 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:41.354621Z"}
2025-05-25 18:48:46,440 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:46.440156Z"}
2025-05-25 18:48:51,543 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:51.543398Z"}
2025-05-25 18:48:56,648 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:56.648632Z"}
2025-05-25 18:49:01,748 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:01.748897Z"}
2025-05-25 18:49:06,835 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:06.835114Z"}
2025-05-25 18:49:11,953 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:11.953167Z"}
2025-05-25 18:49:17,043 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:17.042975Z"}
2025-05-25 18:49:22,131 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:22.131905Z"}
2025-05-25 18:49:27,247 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:27.247879Z"}
2025-05-25 18:49:32,365 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:32.365722Z"}
2025-05-25 18:49:37,450 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:37.450813Z"}
2025-05-25 18:49:42,556 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:42.556507Z"}
2025-05-25 18:49:47,733 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:47.732157Z"}
2025-05-25 18:49:52,827 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:52.826994Z"}
2025-05-25 18:56:25,813 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.813040Z"}
2025-05-25 18:56:25,829 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.829854Z"}
2025-05-25 18:56:25,983 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:25.983491Z"}
2025-05-25 18:56:26,702 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:56:26.701900Z"}
2025-05-25 18:56:26,711 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.711457Z"}
2025-05-25 18:56:26,814 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.814161Z"}
2025-05-25 18:56:26,822 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:56:26.822377Z"}
2025-05-25 18:56:26,829 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.829242Z"}
2025-05-25 18:56:26,851 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.850483Z"}
2025-05-25 18:56:27,211 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.211016Z"}
2025-05-25 18:56:27,227 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.227622Z"}
2025-05-25 18:56:27,272 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.271913Z"}
2025-05-25 18:56:27,279 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.279753Z"}
2025-05-25 18:56:27,700 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:56:27.700315Z"}
2025-05-25 18:56:27,705 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:56:27.705214Z"}
2025-05-25 18:56:27,709 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:56:27.709864Z"}
2025-05-25 18:56:27,723 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.723839Z"}
2025-05-25 18:56:27,730 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.730528Z"}
2025-05-25 18:56:27,747 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.747738Z"}
2025-05-25 18:56:27,761 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.761294Z"}
2025-05-25 18:56:28,070 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:28.070259Z"}
2025-05-25 18:56:33,238 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:33.237884Z"}
2025-05-25 18:56:38,316 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:38.316707Z"}
2025-05-25 18:56:43,402 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:43.402283Z"}
2025-05-25 18:56:48,498 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:48.498285Z"}
2025-05-25 18:56:53,590 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:53.590497Z"}
2025-05-25 18:56:58,683 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:58.683726Z"}
2025-05-25 18:57:03,760 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:03.760435Z"}
2025-05-25 18:57:08,847 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:08.847174Z"}
2025-05-25 18:57:13,934 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:13.934447Z"}
2025-05-25 18:57:19,050 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:19.050515Z"}
2025-05-25 19:01:30,242 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.242244Z"}
2025-05-25 19:01:30,256 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.256800Z"}
2025-05-25 19:01:30,260 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.260026Z"}
2025-05-25 19:01:30,540 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T23:01:30.540050Z"}
2025-05-25 19:01:30,544 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.544876Z"}
2025-05-25 19:01:30,588 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.587751Z"}
2025-05-25 19:01:30,624 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T23:01:30.624308Z"}
2025-05-25 19:01:30,631 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.631470Z"}
2025-05-25 19:01:30,687 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.687613Z"}
2025-05-25 19:01:30,745 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.744985Z"}
2025-05-25 19:01:30,776 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.776673Z"}
2025-05-25 19:01:30,809 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.809217Z"}
2025-05-25 19:01:30,837 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.837682Z"}
2025-05-25 19:01:31,032 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T23:01:31.031999Z"}
2025-05-25 19:01:31,056 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T23:01:31.056726Z"}
2025-05-25 19:01:31,100 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.100850Z"}
2025-05-25 19:01:31,112 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.111346Z"}
2025-05-25 19:01:31,125 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.125518Z"}
2025-05-25 19:01:31,135 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.135681Z"}
2025-05-25 19:01:31,140 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.140644Z"}
2025-05-25 19:01:31,264 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.263406Z"}
2025-05-25 19:01:31,473 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.472968Z"}
2025-05-25 19:01:31,498 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.498262Z"}
2025-05-25 19:01:34,205 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DnP1YHAe... sold 0.2772 SOL of PLS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:34.205803Z"}
2025-05-25 19:01:38,710 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FSxwKvXC... sold 1.9802 SOL of nilsuisou", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:38.709945Z"}
2025-05-25 19:01:41,576 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of BT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:41.576205Z"}
2025-05-25 19:01:43,996 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3KJWMTwq... sold 2.9703 SOL of biocompute", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:43.996669Z"}
2025-05-25 19:01:47,979 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of GOATSEUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:47.979850Z"}
2025-05-25 19:01:48,265 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9h3CegiR... sold 2.2300 SOL of BIOCOMPUTE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:48.264022Z"}
2025-05-25 19:01:49,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BX5pa8eP... sold 2.9703 SOL of bc", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:49.024906Z"}
2025-05-25 19:01:51,404 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.4752 SOL of truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:51.404226Z"}
2025-05-25 19:01:57,862 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of BIOPC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:57.862308Z"}
2025-05-25 19:01:58,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F7BtcvEr... sold 2.4752 SOL of SYBAU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:58.025858Z"}
2025-05-25 19:02:03,091 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: animeQsS... sold 2.9703 SOL of biocomp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:03.091694Z"}
2025-05-25 19:02:04,182 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FR4HKPjq... sold 0.9901 SOL of investment", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.182290Z"}
2025-05-25 19:02:04,999 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2zt4HPCD... sold 4.9505 SOL of VEO3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.998929Z"}
2025-05-25 19:02:08,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: qbrKg1oq... sold 2.8000 SOL of FreeWill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:08.241891Z"}
2025-05-25 19:02:09,558 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 28z37CM9... sold 0.1188 SOL of Alien", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:09.552782Z"}
2025-05-25 19:02:12,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cztdSB5... sold 1.7822 SOL of Brucify ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:12.430532Z"}
2025-05-25 19:02:14,726 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of BULL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.725950Z"}
2025-05-25 19:02:14,957 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H1p7CyPF... sold 4.0000 SOL of Truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.957827Z"}
2025-05-25 19:02:15,224 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5heqWPem... sold 0.0010 SOL of GBIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:15.224122Z"}
2025-05-25 19:02:17,366 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6NiaaKRg... sold 1.4851 SOL of coinbase", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:17.366775Z"}
2025-05-25 19:02:19,378 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of shell", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:19.373117Z"}
2025-05-25 19:02:22,131 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of bt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:22.130403Z"}
2025-05-25 19:02:23,218 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4pMQrW7f... sold 0.9901 SOL of chopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:23.218559Z"}
2025-05-25 19:02:31,518 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CLVNpy5e... sold 0.0990 SOL of ENZO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:31.518577Z"}
2025-05-25 19:02:39,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4fC9hcfS... sold 0.9000 SOL of modl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:39.019897Z"}
2025-05-25 19:02:44,411 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H7JGZzpK... sold 0.9901 SOL of ndnp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:44.410935Z"}
2025-05-25 19:02:47,808 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of MEGA Coin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:47.808211Z"}
2025-05-26 13:15:06,774 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:06.773343Z"}
2025-05-26 13:15:07,412 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:15:07.412258Z"}
2025-05-26 13:15:07,445 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:07.444244Z"}
2025-05-26 13:15:07,547 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:07.545430Z"}
2025-05-26 13:15:07,606 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:15:07.606415Z"}
2025-05-26 13:15:07,750 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:15:07.750123Z"}
2025-05-26 13:15:08,062 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-26T17:15:08.062333Z"}
2025-05-26 13:15:08,254 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-26T17:15:08.253984Z"}
2025-05-26 13:15:08,433 - solana_trading_bot - ERROR - {"event": "Bot crashed: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-26T17:15:08.433688Z"}
2025-05-26 13:15:08,470 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:08.470165Z"}
2025-05-26 13:15:08,479 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-26T17:15:08.479701Z"}
2025-05-26 13:15:08,698 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-26T17:15:08.698152Z"}
2025-05-26 13:15:09,034 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:15:09.034682Z"}
2025-05-26 13:15:09,051 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:15:09.051848Z"}
2025-05-26 13:15:09,070 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:09.070560Z"}
2025-05-26 13:16:46,018 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.018110Z"}
2025-05-26 13:16:46,364 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:16:46.364222Z"}
2025-05-26 13:16:46,371 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.371060Z"}
2025-05-26 13:16:46,410 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.410257Z"}
2025-05-26 13:16:46,458 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:16:46.457938Z"}
2025-05-26 13:16:46,462 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:16:46.462412Z"}
2025-05-26 13:16:47,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 13:16:47,093 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:16:47.092961Z"}
2025-05-26 13:16:47,141 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: name 'CommandHandler' is not defined", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-26T17:16:47.141138Z"}
2025-05-26 13:16:47,173 - solana_trading_bot - ERROR - {"event": "Bot crashed: name 'CommandHandler' is not defined", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-26T17:16:47.173543Z"}
2025-05-26 13:16:47,185 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:47.184550Z"}
2025-05-26 13:16:47,199 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-26T17:16:47.198861Z"}
2025-05-26 13:16:47,208 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-26T17:16:47.208299Z"}
2025-05-26 13:16:47,216 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:16:47.216728Z"}
2025-05-26 13:16:47,223 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:16:47.223577Z"}
2025-05-26 13:16:47,237 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:47.237734Z"}
2025-05-26 13:18:19,800 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.800070Z"}
2025-05-26 13:18:19,953 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:18:19.953874Z"}
2025-05-26 13:18:19,961 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.961908Z"}
2025-05-26 13:18:19,964 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.964073Z"}
2025-05-26 13:18:19,966 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:18:19.966244Z"}
2025-05-26 13:18:19,979 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:19.979192Z"}
2025-05-26 13:18:20,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 13:18:20,571 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:20.571280Z"}
2025-05-26 13:18:20,588 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.587964Z"}
2025-05-26 13:18:20,618 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.618746Z"}
2025-05-26 13:18:20,636 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.636050Z"}
2025-05-26 13:18:20,754 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.754314Z"}
2025-05-26 13:18:20,758 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.758562Z"}
2025-05-26 13:18:20,770 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.770289Z"}
2025-05-26 13:18:20,774 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.773929Z"}
2025-05-26 13:18:20,784 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.782803Z"}
2025-05-26 13:18:20,839 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T17:18:20.839628Z"}
2025-05-26 13:18:21,095 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T17:18:21.095475Z"}
2025-05-26 13:18:21,119 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.118953Z"}
2025-05-26 13:18:21,123 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.123122Z"}
2025-05-26 13:18:21,128 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.128379Z"}
2025-05-26 13:18:21,131 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.131690Z"}
2025-05-26 13:18:21,137 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.136914Z"}
2025-05-26 13:18:21,144 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:18:21.144701Z"}
2025-05-26 13:18:21,147 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.147651Z"}
2025-05-26 13:18:21,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 13:18:21,353 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 13:18:21,362 - telegram.ext.Application - INFO - Application started
2025-05-26 13:18:21,368 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.368061Z"}
2025-05-26 13:18:21,390 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.390763Z"}
2025-05-26 13:18:21,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 13:18:21,459 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:21.459115Z"}
2025-05-26 13:18:21,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:22,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 13:18:23,600 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vKgXDW6... sold 0.9901 SOL of $5t", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:23.600727Z"}
2025-05-26 13:18:25,915 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: VJBkhSqY... sold 0.0134 SOL of MOKO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:25.915403Z"}
2025-05-26 13:18:26,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Rnx8r45... sold 0.1000 SOL of FNQWeqnj", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:26.702398Z"}
2025-05-26 13:18:27,889 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7R8dwmAy... sold 2.9703 SOL of MAYA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:27.889666Z"}
2025-05-26 13:18:28,471 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8uWXcvQ3... sold 2.2300 SOL of maya", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:28.471591Z"}
2025-05-26 13:18:29,157 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of BITAPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:29.157594Z"}
2025-05-26 13:18:31,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:33,739 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gvdn5Znu... sold 0.4950 SOL of RIP K9 Ban", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:33.739358Z"}
2025-05-26 13:18:37,155 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 93dggmM2... sold 0.2475 SOL of plastic", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:37.155621Z"}
2025-05-26 13:18:40,221 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9MygYi1g... sold 1.8000 SOL of COSWA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:40.221293Z"}
2025-05-26 13:18:41,971 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:43,905 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G9RzfqDx... sold 0.9901 SOL of TLC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:43.905545Z"}
2025-05-26 13:18:44,826 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2WUKgkjj... sold 2.3762 SOL of BLUE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:44.826399Z"}
2025-05-26 13:18:45,396 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3TNjowNo... sold 9.9010 SOL of Shimejis", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:45.395862Z"}
2025-05-26 13:18:45,831 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9ASL6KCv... sold 0.9901 SOL of flynak", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:45.830976Z"}
2025-05-26 13:18:50,798 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HUzj85RM... sold 0.0099 SOL of RMOON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:50.798358Z"}
2025-05-26 13:18:51,899 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DU3mYzxY... sold 0.0990 SOL of S&PUSS 500", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:51.899101Z"}
2025-05-26 13:18:52,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:58,573 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 46ozLRrH... sold 0.0000 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:58.573901Z"}
2025-05-26 13:18:58,720 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6oXmzx2i... sold 2.9703 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:58.720563Z"}
2025-05-26 13:18:59,313 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 69NkDB88... sold 0.0000 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:59.313194Z"}
2025-05-26 13:19:01,574 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6zt5ZWwH... sold 1.9802 SOL of USDC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:01.573906Z"}
2025-05-26 13:19:01,625 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:01.625583Z"}
2025-05-26 13:19:02,041 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ESQusYET... sold 1.4851 SOL of BashAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.040968Z"}
2025-05-26 13:19:02,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:02,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DoqM97do... sold 0.9901 SOL of DogLovers", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.472970Z"}
2025-05-26 13:19:02,519 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: sozd8xJe... sold 1.1200 SOL of FrenchSlap", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.519597Z"}
2025-05-26 13:19:03,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2kRnzDco... sold 2.0000 SOL of BANDIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:03.241298Z"}
2025-05-26 13:19:05,157 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 1kKeYdcS... sold 1.2871 SOL of \u2205", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:05.157419Z"}
2025-05-26 13:19:05,315 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 2.1782 SOL of X PACKAGE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:05.315633Z"}
2025-05-26 13:19:10,638 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APVEy4vU... sold 1.4851 SOL of FENT COIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:10.638751Z"}
2025-05-26 13:19:10,729 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DyxNedSJ... sold 0.9901 SOL of avinel", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:10.729105Z"}
2025-05-26 13:19:12,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:12,279 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8zgYte89... sold 0.0178 SOL of FLOCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:12.279624Z"}
2025-05-26 13:19:12,362 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CnZ8TRVU... sold 1.1881 SOL of GMF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:12.362685Z"}
2025-05-26 13:19:13,724 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 622SJuVK... sold 1.2871 SOL of WALKCOIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:13.724101Z"}
2025-05-26 13:19:16,040 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9nM5fhrH... sold 61.3861 SOL of PEPEGROK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:16.040118Z"}
2025-05-26 13:19:22,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:31,838 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EoTWBXEo... sold 1.3861 SOL of bro", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:31.836963Z"}
2025-05-26 13:19:32,630 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:36,433 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G84Yz6fD... sold 1.9802 SOL of Otter", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:36.433491Z"}
2025-05-26 13:19:39,824 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CEUA7zVo... sold 1.6832 SOL of RIPCHARLIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:39.824818Z"}
2025-05-26 13:19:41,349 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ANQvetBM... sold 1.9802 SOL of SOLDEGENS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:41.348938Z"}
2025-05-26 13:19:42,270 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9Dzw5wF9... sold 0.0001 SOL of 0.0.0.0.0.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:42.269772Z"}
2025-05-26 13:19:42,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:44,112 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of TAYLOR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:44.112720Z"}
2025-05-26 13:19:44,420 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CeU2b1KW... sold 0.1980 SOL of OLDGECKO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:44.420331Z"}
2025-05-26 13:19:45,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:45.022442Z"}
2025-05-26 13:19:53,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:53,032 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmgKiEqo... sold 0.4950 SOL of SpaceX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.032661Z"}
2025-05-26 13:19:53,704 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2DGWmnQq... sold 0.9901 SOL of POT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.704425Z"}
2025-05-26 13:19:53,928 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of Jan44734", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.928109Z"}
2025-05-26 13:19:55,115 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9PXd1QDB... sold 2.9703 SOL of FART", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:55.115488Z"}
2025-05-26 13:19:56,948 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G3xCvMCH... sold 2.7525 SOL of FOUNDRY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:56.948580Z"}
2025-05-26 13:20:02,237 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CXUw3Jp2... sold 1.6832 SOL of avcore", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:02.237276Z"}
2025-05-26 13:20:03,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:03,331 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5aD1fNHZ... sold 3.0000 SOL of PIZZI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:03.331347Z"}
2025-05-26 13:20:05,058 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Hnv9TkQd... sold 1.9000 SOL of Oyajichi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:05.058070Z"}
2025-05-26 13:20:05,338 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of FAGWHEEL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:05.338293Z"}
2025-05-26 13:20:08,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3PC5geEF... sold 1.9802 SOL of Don Tusk", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:08.381570Z"}
2025-05-26 13:20:12,989 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CS2rHpJj... sold 5.0000 SOL of Roadster", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:12.989807Z"}
2025-05-26 13:20:13,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:13,917 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vKgXDW6... sold 0.9901 SOL of \ud83e\udd23", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:13.916095Z"}
2025-05-26 13:20:17,442 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of GOONDAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:17.442884Z"}
2025-05-26 13:20:17,737 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RM4b8kT... sold 0.0020 SOL of Bob", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:17.737904Z"}
2025-05-26 13:20:18,655 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DU3mYzxY... sold 0.0990 SOL of S&PUSSY 69", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:18.655894Z"}
2025-05-26 13:20:19,335 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of Toberichal", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:19.334932Z"}
2025-05-26 13:20:20,670 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Ei4X8Lyc... sold 5.9406 SOL of h1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:20.670453Z"}
2025-05-26 13:20:20,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:20.702190Z"}
2025-05-26 13:20:22,563 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of 0xblastard", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:22.563080Z"}
2025-05-26 13:20:23,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:25,584 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: WRXFrF1p... sold 0.9901 SOL of NIC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:25.584197Z"}
2025-05-26 13:20:25,655 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3hwS51f1... sold 1.9802 SOL of firecoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:25.655844Z"}
2025-05-26 13:20:29,694 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gvdn5Znu... sold 0.9901 SOL of RIP K9 Ban", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:29.694222Z"}
2025-05-26 13:20:29,764 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GewBprxs... sold 5.0000 SOL of Roadster", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:29.764285Z"}
2025-05-26 13:20:33,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:36,950 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ZeU9Lbc... sold 3.0000 SOL of JAMES", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:36.950154Z"}
2025-05-26 13:20:41,608 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of GOONDAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:41.608504Z"}
2025-05-26 13:20:43,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:47,396 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 93dggmM2... sold 0.2475 SOL of FAGWHEEL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:47.396155Z"}
2025-05-26 13:20:51,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BjCcu5RH... sold 0.4987 SOL of WW3MUPPETS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:51.081998Z"}
2025-05-26 13:20:52,617 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9MusJmn6... sold 0.3960 SOL of Maya", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:52.616986Z"}
2025-05-26 13:20:53,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:57,730 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FBo4KX47... sold 0.5941 SOL of Argt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:57.730237Z"}
2025-05-26 13:21:02,380 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 4.9505 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:02.380411Z"}
2025-05-26 13:21:02,540 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of P33STUDiO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:02.540092Z"}
2025-05-26 13:21:03,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:09,198 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: JE754JqB... sold 0.1000 SOL of BRASIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:09.198284Z"}
2025-05-26 13:21:10,435 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dp1XWk5a... sold 1.1990 SOL of who", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:10.434832Z"}
2025-05-26 13:21:11,050 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ZUkJdSLE... sold 2.0000 SOL of \ud83e\udd90", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:11.050678Z"}
2025-05-26 13:21:14,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:15,048 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of bike", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:15.047907Z"}
2025-05-26 13:21:16,585 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4b1Xr46u... sold 0.1980 SOL of prison", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:16.585636Z"}
2025-05-26 13:21:24,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:25,179 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of DRAGAPEX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:25.179674Z"}
2025-05-26 13:21:28,559 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ANQvetBM... sold 1.9802 SOL of MAGNUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:28.558968Z"}
2025-05-26 13:21:32,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F4dmmtM7... sold 0.4257 SOL of BEACH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:32.339016Z"}
2025-05-26 13:21:32,477 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: JE754JqB... sold 0.1000 SOL of BRASIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:32.477714Z"}
2025-05-26 13:21:34,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:35,931 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fy3XcLYZ... sold 0.2970 SOL of \ud83d\ude80", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:35.931710Z"}
2025-05-26 13:21:36,239 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8rSeJJRZ... sold 0.0099 SOL of TESTF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:36.239480Z"}
2025-05-26 13:21:37,467 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DKuNKQed... sold 1.6337 SOL of CWB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:37.467402Z"}
2025-05-26 13:21:40,539 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: oNJ2nas9... sold 1.4851 SOL of hyperchill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:40.539554Z"}
2025-05-26 13:21:44,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:51,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 1.5446 SOL of ROSS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:51.102368Z"}
2025-05-26 13:21:52,214 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AqVnksyB... sold 0.9901 SOL of ELON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.214535Z"}
2025-05-26 13:21:52,257 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: B2CRi1hs... sold 0.1000 SOL of MAYA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.257713Z"}
2025-05-26 13:21:52,307 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 667AWcos... sold 2.9703 SOL of LUPIA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.307299Z"}
2025-05-26 13:21:54,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:56,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EoWwfq9N... sold 0.0010 SOL of botcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:56.019824Z"}
2025-05-26 13:21:57,435 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9sCcAxe5... sold 4.0000 SOL of WT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:57.435894Z"}
2025-05-26 13:21:59,279 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Hw5w1axG... sold 0.0593 SOL of SETH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:59.279147Z"}
2025-05-26 13:22:03,272 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APVEy4vU... sold 1.4851 SOL of STD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:03.272506Z"}
2025-05-26 13:22:04,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:09,330 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5uvXxXjL... sold 1.9000 SOL of BarbieGirl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:09.329117Z"}
2025-05-26 13:22:09,941 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EwyvPTKi... sold 1.1000 SOL of CryptoSwar", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:09.941377Z"}
2025-05-26 13:22:13,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of OggyStealS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:13.102257Z"}
2025-05-26 13:22:13,153 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7i6bfTP8... sold 1.4900 SOL of Cubikka", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:13.152943Z"}
2025-05-26 13:22:14,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:15,270 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of CRYPT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:15.270871Z"}
2025-05-26 13:22:15,994 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CrMSFMdW... sold 1.1881 SOL of TRRLO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:15.994479Z"}
2025-05-26 13:22:19,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DSVNmMZi... sold 0.9901 SOL of STARSHIP 3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:19.339578Z"}
2025-05-26 13:22:19,365 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 44nFNkKU... sold 0.9901 SOL of pgooner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:19.365412Z"}
2025-05-26 13:22:20,965 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AAPkNToN... sold 0.2077 SOL of moondog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:20.965896Z"}
2025-05-26 13:22:21,432 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2xKDETTh... sold 2.9703 SOL of RIP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:21.432453Z"}
2025-05-26 13:22:21,507 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FjeaKDFA... sold 2.9703 SOL of Mr Bean", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:21.507706Z"}
2025-05-26 13:22:24,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:25,579 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9maDquBn... sold 0.0297 SOL of LockN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:25.579458Z"}
2025-05-26 13:22:26,500 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: vGwtpGFM... sold 0.0010 SOL of CSR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:26.500638Z"}
2025-05-26 13:22:29,504 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 66666KoH... sold 1.9802 SOL of Mr Bean", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:29.504817Z"}
2025-05-26 13:22:34,341 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HM11w5fe... sold 0.1188 SOL of squid", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:34.341679Z"}
2025-05-26 13:22:35,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:36,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9r8NuiwA... sold 3.8565 SOL of SUPR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:36.022516Z"}
2025-05-26 13:22:36,944 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2cJN9h83... sold 1.6000 SOL of ButtHurt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:36.943918Z"}
2025-05-26 13:22:38,508 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GGbQBvbq... sold 0.8911 SOL of LR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:38.508559Z"}
2025-05-26 13:22:43,239 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoLTdQey... sold 0.9802 SOL of 59s", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:43.239162Z"}
2025-05-26 13:22:43,295 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2Zobi8ji... sold 0.1980 SOL of fly", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:43.295651Z"}
2025-05-26 13:22:45,076 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of IA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:45.075955Z"}
2025-05-26 13:22:45,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:45,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of TRUMPACCTS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:45.430330Z"}
2025-05-26 13:22:54,453 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVWP2wwf... sold 3.0000 SOL of send", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:54.452980Z"}
2025-05-26 13:22:55,202 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 723CcrTR... sold 0.7733 SOL of MOG16", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:55.202795Z"}
2025-05-26 13:22:55,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:57,821 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AV4zBZSe... sold 1.5842 SOL of MR BEAN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:57.821671Z"}
2025-05-26 13:22:59,060 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FaPWKVMi... sold 1.0891 SOL of PEPLUSH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:59.060775Z"}
2025-05-26 13:23:03,593 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8R7Do5RU... sold 0.0079 SOL of BTC BOOM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:23:03.593473Z"}
2025-05-26 13:23:04,460 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of TIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:23:04.460403Z"}
