2025-05-25 17:54:55,737 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:55.736854Z"}
2025-05-25 17:54:56,397 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:54:56.397454Z"}
2025-05-25 17:54:56,403 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.403735Z"}
2025-05-25 17:54:56,413 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.412323Z"}
2025-05-25 17:54:56,447 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:54:56.447070Z"}
2025-05-25 17:54:56,487 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:54:56.487287Z"}
2025-05-25 17:54:56,498 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:54:56.498359Z"}
2025-05-25 17:54:56,516 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:54:56.516584Z"}
2025-05-25 17:54:56,528 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:54:56.528819Z"}
2025-05-25 17:54:56,537 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.537572Z"}
2025-05-25 17:54:56,543 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:54:56.543847Z"}
2025-05-25 17:54:56,550 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.550749Z"}
2025-05-25 17:54:56,560 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.560229Z"}
2025-05-25 17:54:56,566 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:54:56.566354Z"}
2025-05-25 17:54:56,573 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.572971Z"}
2025-05-25 17:56:10,941 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:10.941571Z"}
2025-05-25 17:56:11,118 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:56:11.118560Z"}
2025-05-25 17:56:11,124 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.124063Z"}
2025-05-25 17:56:11,129 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.129341Z"}
2025-05-25 17:56:11,135 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:56:11.135574Z"}
2025-05-25 17:56:11,150 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:56:11.150751Z"}
2025-05-25 17:56:11,191 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:56:11.191162Z"}
2025-05-25 17:56:11,219 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:56:11.219027Z"}
2025-05-25 17:56:11,235 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:56:11.235235Z"}
2025-05-25 17:56:11,248 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.248662Z"}
2025-05-25 17:56:11,276 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:56:11.276372Z"}
2025-05-25 17:56:11,289 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.289307Z"}
2025-05-25 17:56:11,301 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.301738Z"}
2025-05-25 17:56:11,311 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:56:11.311540Z"}
2025-05-25 17:56:11,315 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.315073Z"}
2025-05-25 17:57:35,285 - test_bot - INFO - {"event": "\u23f0 Test started at: 2025-05-25 17:57:35", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.285544Z"}
2025-05-25 17:57:35,294 - test_bot - INFO - {"event": "\ud83d\ude80 Starting Minimal Solana Trading Bot Test...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.293997Z"}
2025-05-25 17:57:35,298 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.298890Z"}
2025-05-25 17:57:35,318 - test_bot - INFO - {"event": "\ud83d\udcca Testing database connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.318141Z"}
2025-05-25 17:57:35,423 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:57:35.423852Z"}
2025-05-25 17:57:35,426 - test_bot - INFO - {"event": "\u2705 Database initialized successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.426162Z"}
2025-05-25 17:57:35,428 - test_bot - INFO - {"event": "\u2699\ufe0f Testing configuration...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.428409Z"}
2025-05-25 17:57:35,439 - test_bot - INFO - {"event": "RPC URL: https://api.mainnet-beta.solana.com", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.439508Z"}
2025-05-25 17:57:35,441 - test_bot - INFO - {"event": "Pump.fun monitoring: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.441886Z"}
2025-05-25 17:57:35,444 - test_bot - INFO - {"event": "Copy trading: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.444079Z"}
2025-05-25 17:57:35,476 - test_bot - INFO - {"event": "Telegram bot: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.476631Z"}
2025-05-25 17:57:35,479 - test_bot - INFO - {"event": "\u2705 Configuration loaded successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.478979Z"}
2025-05-25 17:57:35,489 - test_bot - INFO - {"event": "\ud83d\ude80 Testing Pump.fun API connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.489327Z"}
2025-05-25 17:57:35,493 - test_bot - ERROR - {"event": "\u274c Pump.fun API test failed: 'PumpFunMonitor' object has no attribute 'initialize'", "logger": "test_bot", "level": "error", "timestamp": "2025-05-25T21:57:35.493535Z"}
2025-05-25 17:57:35,510 - test_bot - INFO - {"event": "\u23f1\ufe0f Running monitoring test for 30 seconds...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.509941Z"}
2025-05-25 17:57:35,519 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:57:35.519199Z"}
2025-05-25 17:57:35,670 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:35.669959Z"}
2025-05-25 17:57:40,754 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:40.754005Z"}
2025-05-25 17:57:45,838 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:45.838399Z"}
2025-05-25 17:57:50,923 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:50.923363Z"}
2025-05-25 17:57:56,042 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:56.042403Z"}
2025-05-25 17:58:01,133 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:58:01.133000Z"}
2025-05-25 17:58:05,521 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.521289Z"}
2025-05-25 17:58:05,524 - test_bot - INFO - {"event": "\u2705 Monitoring test completed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.523943Z"}
2025-05-25 17:58:05,526 - test_bot - INFO - {"event": "\ud83e\uddf9 Cleaning up...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.526420Z"}
2025-05-25 17:58:05,541 - test_bot - INFO - {"event": "\u2705 Database connections closed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.541837Z"}
2025-05-25 17:58:05,555 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.555592Z"}
2025-05-25 17:58:05,557 - test_bot - INFO - {"event": "\ud83c\udf89 All core functionality tests passed!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.557839Z"}
2025-05-25 17:58:05,568 - test_bot - INFO - {"event": "\u2705 The Solana Trading Bot core is working correctly", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.563329Z"}
2025-05-25 17:58:05,577 - test_bot - INFO - {"event": "\n\ud83d\ude80 CORE FUNCTIONALITY TEST: PASSED", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.577430Z"}
2025-05-25 17:58:05,591 - test_bot - INFO - {"event": "The bot's core systems are working correctly!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.591562Z"}
2025-05-25 17:58:05,623 - test_bot - INFO - {"event": "You can now run the full bot with: python run_bot.py", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.623162Z"}
2025-05-25 17:58:05,625 - test_bot - INFO - {"event": "\u23f0 Test completed at: 2025-05-25 17:58:05", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.625613Z"}
2025-05-25 17:58:05,640 - PumpFunMonitor - INFO - {"event": "New token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.640499Z"}
2025-05-25 17:58:05,652 - PumpFunMonitor - INFO - {"event": "Trending token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.652611Z"}
2025-05-25 17:58:05,658 - PumpFunMonitor - INFO - {"event": "Cleanup task cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.658466Z"}
2025-05-25 18:46:31,456 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.456573Z"}
2025-05-25 18:46:31,622 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:46:31.622346Z"}
2025-05-25 18:46:31,625 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.625427Z"}
2025-05-25 18:46:31,627 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.627806Z"}
2025-05-25 18:46:31,630 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:46:31.630520Z"}
2025-05-25 18:46:31,655 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T22:46:31.655722Z"}
2025-05-25 18:46:31,663 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T22:46:31.663001Z"}
2025-05-25 18:46:31,742 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T22:46:31.741884Z"}
2025-05-25 18:46:31,774 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T22:46:31.763601Z"}
2025-05-25 18:46:31,781 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.781163Z"}
2025-05-25 18:46:31,820 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T22:46:31.820381Z"}
2025-05-25 18:46:31,869 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.869355Z"}
2025-05-25 18:46:31,894 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.894049Z"}
2025-05-25 18:46:31,905 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T22:46:31.905015Z"}
2025-05-25 18:46:31,935 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.935541Z"}
2025-05-25 18:47:38,973 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.973327Z"}
2025-05-25 18:47:38,993 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.993781Z"}
2025-05-25 18:47:39,023 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.023435Z"}
2025-05-25 18:47:39,145 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:47:39.145619Z"}
2025-05-25 18:47:39,158 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.158521Z"}
2025-05-25 18:47:39,163 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.163394Z"}
2025-05-25 18:47:39,177 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:47:39.177044Z"}
2025-05-25 18:47:39,209 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.209601Z"}
2025-05-25 18:47:39,228 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.228144Z"}
2025-05-25 18:47:39,288 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.288079Z"}
2025-05-25 18:47:39,296 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.296179Z"}
2025-05-25 18:47:39,326 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.326022Z"}
2025-05-25 18:47:39,391 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.380255Z"}
2025-05-25 18:47:39,613 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:47:39.613500Z"}
2025-05-25 18:47:39,623 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:47:39.623665Z"}
2025-05-25 18:47:39,628 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:47:39.628676Z"}
2025-05-25 18:47:39,639 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.639707Z"}
2025-05-25 18:47:39,647 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.646932Z"}
2025-05-25 18:47:39,658 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.658237Z"}
2025-05-25 18:47:39,665 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.665154Z"}
2025-05-25 18:47:39,843 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:39.843811Z"}
2025-05-25 18:47:44,944 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:44.944444Z"}
2025-05-25 18:47:50,051 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:50.051812Z"}
2025-05-25 18:47:55,321 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:55.318947Z"}
2025-05-25 18:48:00,435 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:00.435812Z"}
2025-05-25 18:48:05,537 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:05.537077Z"}
2025-05-25 18:48:10,628 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:10.628524Z"}
2025-05-25 18:48:15,726 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:15.726263Z"}
2025-05-25 18:48:20,812 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:20.812546Z"}
2025-05-25 18:48:25,913 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:25.913146Z"}
2025-05-25 18:48:31,005 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:31.005795Z"}
2025-05-25 18:48:36,241 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:36.241753Z"}
2025-05-25 18:48:41,354 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:41.354621Z"}
2025-05-25 18:48:46,440 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:46.440156Z"}
2025-05-25 18:48:51,543 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:51.543398Z"}
2025-05-25 18:48:56,648 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:56.648632Z"}
2025-05-25 18:49:01,748 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:01.748897Z"}
2025-05-25 18:49:06,835 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:06.835114Z"}
2025-05-25 18:49:11,953 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:11.953167Z"}
2025-05-25 18:49:17,043 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:17.042975Z"}
2025-05-25 18:49:22,131 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:22.131905Z"}
2025-05-25 18:49:27,247 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:27.247879Z"}
2025-05-25 18:49:32,365 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:32.365722Z"}
2025-05-25 18:49:37,450 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:37.450813Z"}
2025-05-25 18:49:42,556 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:42.556507Z"}
2025-05-25 18:49:47,733 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:47.732157Z"}
2025-05-25 18:49:52,827 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:52.826994Z"}
2025-05-25 18:56:25,813 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.813040Z"}
2025-05-25 18:56:25,829 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.829854Z"}
2025-05-25 18:56:25,983 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:25.983491Z"}
2025-05-25 18:56:26,702 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:56:26.701900Z"}
2025-05-25 18:56:26,711 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.711457Z"}
2025-05-25 18:56:26,814 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.814161Z"}
2025-05-25 18:56:26,822 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:56:26.822377Z"}
2025-05-25 18:56:26,829 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.829242Z"}
2025-05-25 18:56:26,851 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.850483Z"}
2025-05-25 18:56:27,211 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.211016Z"}
2025-05-25 18:56:27,227 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.227622Z"}
2025-05-25 18:56:27,272 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.271913Z"}
2025-05-25 18:56:27,279 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.279753Z"}
2025-05-25 18:56:27,700 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:56:27.700315Z"}
2025-05-25 18:56:27,705 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:56:27.705214Z"}
2025-05-25 18:56:27,709 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:56:27.709864Z"}
2025-05-25 18:56:27,723 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.723839Z"}
2025-05-25 18:56:27,730 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.730528Z"}
2025-05-25 18:56:27,747 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.747738Z"}
2025-05-25 18:56:27,761 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.761294Z"}
2025-05-25 18:56:28,070 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:28.070259Z"}
2025-05-25 18:56:33,238 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:33.237884Z"}
2025-05-25 18:56:38,316 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:38.316707Z"}
2025-05-25 18:56:43,402 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:43.402283Z"}
2025-05-25 18:56:48,498 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:48.498285Z"}
2025-05-25 18:56:53,590 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:53.590497Z"}
2025-05-25 18:56:58,683 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:58.683726Z"}
2025-05-25 18:57:03,760 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:03.760435Z"}
2025-05-25 18:57:08,847 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:08.847174Z"}
2025-05-25 18:57:13,934 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:13.934447Z"}
2025-05-25 18:57:19,050 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:19.050515Z"}
2025-05-25 19:01:30,242 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.242244Z"}
2025-05-25 19:01:30,256 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.256800Z"}
2025-05-25 19:01:30,260 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.260026Z"}
2025-05-25 19:01:30,540 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T23:01:30.540050Z"}
2025-05-25 19:01:30,544 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.544876Z"}
2025-05-25 19:01:30,588 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.587751Z"}
2025-05-25 19:01:30,624 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T23:01:30.624308Z"}
2025-05-25 19:01:30,631 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.631470Z"}
2025-05-25 19:01:30,687 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.687613Z"}
2025-05-25 19:01:30,745 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.744985Z"}
2025-05-25 19:01:30,776 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.776673Z"}
2025-05-25 19:01:30,809 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.809217Z"}
2025-05-25 19:01:30,837 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.837682Z"}
2025-05-25 19:01:31,032 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T23:01:31.031999Z"}
2025-05-25 19:01:31,056 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T23:01:31.056726Z"}
2025-05-25 19:01:31,100 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.100850Z"}
2025-05-25 19:01:31,112 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.111346Z"}
2025-05-25 19:01:31,125 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.125518Z"}
2025-05-25 19:01:31,135 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.135681Z"}
2025-05-25 19:01:31,140 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.140644Z"}
2025-05-25 19:01:31,264 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.263406Z"}
2025-05-25 19:01:31,473 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.472968Z"}
2025-05-25 19:01:31,498 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.498262Z"}
2025-05-25 19:01:34,205 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DnP1YHAe... sold 0.2772 SOL of PLS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:34.205803Z"}
2025-05-25 19:01:38,710 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FSxwKvXC... sold 1.9802 SOL of nilsuisou", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:38.709945Z"}
2025-05-25 19:01:41,576 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of BT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:41.576205Z"}
2025-05-25 19:01:43,996 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3KJWMTwq... sold 2.9703 SOL of biocompute", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:43.996669Z"}
2025-05-25 19:01:47,979 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of GOATSEUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:47.979850Z"}
2025-05-25 19:01:48,265 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9h3CegiR... sold 2.2300 SOL of BIOCOMPUTE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:48.264022Z"}
2025-05-25 19:01:49,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BX5pa8eP... sold 2.9703 SOL of bc", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:49.024906Z"}
2025-05-25 19:01:51,404 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.4752 SOL of truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:51.404226Z"}
2025-05-25 19:01:57,862 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of BIOPC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:57.862308Z"}
2025-05-25 19:01:58,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F7BtcvEr... sold 2.4752 SOL of SYBAU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:58.025858Z"}
2025-05-25 19:02:03,091 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: animeQsS... sold 2.9703 SOL of biocomp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:03.091694Z"}
2025-05-25 19:02:04,182 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FR4HKPjq... sold 0.9901 SOL of investment", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.182290Z"}
2025-05-25 19:02:04,999 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2zt4HPCD... sold 4.9505 SOL of VEO3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.998929Z"}
2025-05-25 19:02:08,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: qbrKg1oq... sold 2.8000 SOL of FreeWill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:08.241891Z"}
2025-05-25 19:02:09,558 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 28z37CM9... sold 0.1188 SOL of Alien", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:09.552782Z"}
2025-05-25 19:02:12,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cztdSB5... sold 1.7822 SOL of Brucify ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:12.430532Z"}
2025-05-25 19:02:14,726 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of BULL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.725950Z"}
2025-05-25 19:02:14,957 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H1p7CyPF... sold 4.0000 SOL of Truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.957827Z"}
2025-05-25 19:02:15,224 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5heqWPem... sold 0.0010 SOL of GBIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:15.224122Z"}
2025-05-25 19:02:17,366 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6NiaaKRg... sold 1.4851 SOL of coinbase", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:17.366775Z"}
2025-05-25 19:02:19,378 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of shell", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:19.373117Z"}
2025-05-25 19:02:22,131 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of bt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:22.130403Z"}
2025-05-25 19:02:23,218 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4pMQrW7f... sold 0.9901 SOL of chopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:23.218559Z"}
2025-05-25 19:02:31,518 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CLVNpy5e... sold 0.0990 SOL of ENZO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:31.518577Z"}
2025-05-25 19:02:39,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4fC9hcfS... sold 0.9000 SOL of modl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:39.019897Z"}
2025-05-25 19:02:44,411 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H7JGZzpK... sold 0.9901 SOL of ndnp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:44.410935Z"}
2025-05-25 19:02:47,808 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of MEGA Coin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:47.808211Z"}
2025-05-26 13:15:06,774 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:06.773343Z"}
2025-05-26 13:15:07,412 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:15:07.412258Z"}
2025-05-26 13:15:07,445 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:07.444244Z"}
2025-05-26 13:15:07,547 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:07.545430Z"}
2025-05-26 13:15:07,606 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:15:07.606415Z"}
2025-05-26 13:15:07,750 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:15:07.750123Z"}
2025-05-26 13:15:08,062 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-26T17:15:08.062333Z"}
2025-05-26 13:15:08,254 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-26T17:15:08.253984Z"}
2025-05-26 13:15:08,433 - solana_trading_bot - ERROR - {"event": "Bot crashed: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-26T17:15:08.433688Z"}
2025-05-26 13:15:08,470 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:08.470165Z"}
2025-05-26 13:15:08,479 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-26T17:15:08.479701Z"}
2025-05-26 13:15:08,698 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-26T17:15:08.698152Z"}
2025-05-26 13:15:09,034 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:15:09.034682Z"}
2025-05-26 13:15:09,051 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:15:09.051848Z"}
2025-05-26 13:15:09,070 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:09.070560Z"}
2025-05-26 13:16:46,018 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.018110Z"}
2025-05-26 13:16:46,364 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:16:46.364222Z"}
2025-05-26 13:16:46,371 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.371060Z"}
2025-05-26 13:16:46,410 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.410257Z"}
2025-05-26 13:16:46,458 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:16:46.457938Z"}
2025-05-26 13:16:46,462 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:16:46.462412Z"}
2025-05-26 13:16:47,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 13:16:47,093 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:16:47.092961Z"}
2025-05-26 13:16:47,141 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: name 'CommandHandler' is not defined", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-26T17:16:47.141138Z"}
2025-05-26 13:16:47,173 - solana_trading_bot - ERROR - {"event": "Bot crashed: name 'CommandHandler' is not defined", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-26T17:16:47.173543Z"}
2025-05-26 13:16:47,185 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:47.184550Z"}
2025-05-26 13:16:47,199 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-26T17:16:47.198861Z"}
2025-05-26 13:16:47,208 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-26T17:16:47.208299Z"}
2025-05-26 13:16:47,216 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:16:47.216728Z"}
2025-05-26 13:16:47,223 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:16:47.223577Z"}
2025-05-26 13:16:47,237 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:47.237734Z"}
2025-05-26 13:18:19,800 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.800070Z"}
2025-05-26 13:18:19,953 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:18:19.953874Z"}
2025-05-26 13:18:19,961 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.961908Z"}
2025-05-26 13:18:19,964 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.964073Z"}
2025-05-26 13:18:19,966 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:18:19.966244Z"}
2025-05-26 13:18:19,979 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:19.979192Z"}
2025-05-26 13:18:20,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 13:18:20,571 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:20.571280Z"}
2025-05-26 13:18:20,588 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.587964Z"}
2025-05-26 13:18:20,618 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.618746Z"}
2025-05-26 13:18:20,636 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.636050Z"}
2025-05-26 13:18:20,754 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.754314Z"}
2025-05-26 13:18:20,758 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.758562Z"}
2025-05-26 13:18:20,770 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.770289Z"}
2025-05-26 13:18:20,774 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.773929Z"}
2025-05-26 13:18:20,784 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.782803Z"}
2025-05-26 13:18:20,839 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T17:18:20.839628Z"}
2025-05-26 13:18:21,095 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T17:18:21.095475Z"}
2025-05-26 13:18:21,119 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.118953Z"}
2025-05-26 13:18:21,123 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.123122Z"}
2025-05-26 13:18:21,128 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.128379Z"}
2025-05-26 13:18:21,131 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.131690Z"}
2025-05-26 13:18:21,137 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.136914Z"}
2025-05-26 13:18:21,144 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:18:21.144701Z"}
2025-05-26 13:18:21,147 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.147651Z"}
2025-05-26 13:18:21,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 13:18:21,353 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 13:18:21,362 - telegram.ext.Application - INFO - Application started
2025-05-26 13:18:21,368 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.368061Z"}
2025-05-26 13:18:21,390 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.390763Z"}
2025-05-26 13:18:21,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 13:18:21,459 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:21.459115Z"}
2025-05-26 13:18:21,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:22,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 13:18:23,600 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vKgXDW6... sold 0.9901 SOL of $5t", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:23.600727Z"}
2025-05-26 13:18:25,915 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: VJBkhSqY... sold 0.0134 SOL of MOKO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:25.915403Z"}
2025-05-26 13:18:26,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Rnx8r45... sold 0.1000 SOL of FNQWeqnj", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:26.702398Z"}
2025-05-26 13:18:27,889 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7R8dwmAy... sold 2.9703 SOL of MAYA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:27.889666Z"}
2025-05-26 13:18:28,471 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8uWXcvQ3... sold 2.2300 SOL of maya", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:28.471591Z"}
2025-05-26 13:18:29,157 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of BITAPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:29.157594Z"}
2025-05-26 13:18:31,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:33,739 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gvdn5Znu... sold 0.4950 SOL of RIP K9 Ban", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:33.739358Z"}
2025-05-26 13:18:37,155 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 93dggmM2... sold 0.2475 SOL of plastic", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:37.155621Z"}
2025-05-26 13:18:40,221 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9MygYi1g... sold 1.8000 SOL of COSWA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:40.221293Z"}
2025-05-26 13:18:41,971 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:43,905 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G9RzfqDx... sold 0.9901 SOL of TLC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:43.905545Z"}
2025-05-26 13:18:44,826 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2WUKgkjj... sold 2.3762 SOL of BLUE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:44.826399Z"}
2025-05-26 13:18:45,396 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3TNjowNo... sold 9.9010 SOL of Shimejis", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:45.395862Z"}
2025-05-26 13:18:45,831 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9ASL6KCv... sold 0.9901 SOL of flynak", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:45.830976Z"}
2025-05-26 13:18:50,798 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HUzj85RM... sold 0.0099 SOL of RMOON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:50.798358Z"}
2025-05-26 13:18:51,899 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DU3mYzxY... sold 0.0990 SOL of S&PUSS 500", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:51.899101Z"}
2025-05-26 13:18:52,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:58,573 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 46ozLRrH... sold 0.0000 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:58.573901Z"}
2025-05-26 13:18:58,720 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6oXmzx2i... sold 2.9703 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:58.720563Z"}
2025-05-26 13:18:59,313 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 69NkDB88... sold 0.0000 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:59.313194Z"}
2025-05-26 13:19:01,574 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6zt5ZWwH... sold 1.9802 SOL of USDC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:01.573906Z"}
2025-05-26 13:19:01,625 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:01.625583Z"}
2025-05-26 13:19:02,041 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ESQusYET... sold 1.4851 SOL of BashAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.040968Z"}
2025-05-26 13:19:02,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:02,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DoqM97do... sold 0.9901 SOL of DogLovers", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.472970Z"}
2025-05-26 13:19:02,519 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: sozd8xJe... sold 1.1200 SOL of FrenchSlap", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.519597Z"}
2025-05-26 13:19:03,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2kRnzDco... sold 2.0000 SOL of BANDIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:03.241298Z"}
2025-05-26 13:19:05,157 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 1kKeYdcS... sold 1.2871 SOL of \u2205", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:05.157419Z"}
2025-05-26 13:19:05,315 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 2.1782 SOL of X PACKAGE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:05.315633Z"}
2025-05-26 13:19:10,638 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APVEy4vU... sold 1.4851 SOL of FENT COIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:10.638751Z"}
2025-05-26 13:19:10,729 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DyxNedSJ... sold 0.9901 SOL of avinel", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:10.729105Z"}
2025-05-26 13:19:12,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:12,279 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8zgYte89... sold 0.0178 SOL of FLOCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:12.279624Z"}
2025-05-26 13:19:12,362 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CnZ8TRVU... sold 1.1881 SOL of GMF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:12.362685Z"}
2025-05-26 13:19:13,724 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 622SJuVK... sold 1.2871 SOL of WALKCOIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:13.724101Z"}
2025-05-26 13:19:16,040 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9nM5fhrH... sold 61.3861 SOL of PEPEGROK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:16.040118Z"}
2025-05-26 13:19:22,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:31,838 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EoTWBXEo... sold 1.3861 SOL of bro", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:31.836963Z"}
2025-05-26 13:19:32,630 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:36,433 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G84Yz6fD... sold 1.9802 SOL of Otter", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:36.433491Z"}
2025-05-26 13:19:39,824 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CEUA7zVo... sold 1.6832 SOL of RIPCHARLIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:39.824818Z"}
2025-05-26 13:19:41,349 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ANQvetBM... sold 1.9802 SOL of SOLDEGENS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:41.348938Z"}
2025-05-26 13:19:42,270 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9Dzw5wF9... sold 0.0001 SOL of 0.0.0.0.0.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:42.269772Z"}
2025-05-26 13:19:42,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:44,112 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of TAYLOR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:44.112720Z"}
2025-05-26 13:19:44,420 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CeU2b1KW... sold 0.1980 SOL of OLDGECKO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:44.420331Z"}
2025-05-26 13:19:45,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:45.022442Z"}
2025-05-26 13:19:53,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:53,032 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmgKiEqo... sold 0.4950 SOL of SpaceX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.032661Z"}
2025-05-26 13:19:53,704 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2DGWmnQq... sold 0.9901 SOL of POT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.704425Z"}
2025-05-26 13:19:53,928 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of Jan44734", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.928109Z"}
2025-05-26 13:19:55,115 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9PXd1QDB... sold 2.9703 SOL of FART", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:55.115488Z"}
2025-05-26 13:19:56,948 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G3xCvMCH... sold 2.7525 SOL of FOUNDRY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:56.948580Z"}
2025-05-26 13:20:02,237 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CXUw3Jp2... sold 1.6832 SOL of avcore", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:02.237276Z"}
2025-05-26 13:20:03,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:03,331 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5aD1fNHZ... sold 3.0000 SOL of PIZZI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:03.331347Z"}
2025-05-26 13:20:05,058 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Hnv9TkQd... sold 1.9000 SOL of Oyajichi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:05.058070Z"}
2025-05-26 13:20:05,338 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of FAGWHEEL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:05.338293Z"}
2025-05-26 13:20:08,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3PC5geEF... sold 1.9802 SOL of Don Tusk", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:08.381570Z"}
2025-05-26 13:20:12,989 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CS2rHpJj... sold 5.0000 SOL of Roadster", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:12.989807Z"}
2025-05-26 13:20:13,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:13,917 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vKgXDW6... sold 0.9901 SOL of \ud83e\udd23", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:13.916095Z"}
2025-05-26 13:20:17,442 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of GOONDAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:17.442884Z"}
2025-05-26 13:20:17,737 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RM4b8kT... sold 0.0020 SOL of Bob", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:17.737904Z"}
2025-05-26 13:20:18,655 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DU3mYzxY... sold 0.0990 SOL of S&PUSSY 69", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:18.655894Z"}
2025-05-26 13:20:19,335 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of Toberichal", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:19.334932Z"}
2025-05-26 13:20:20,670 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Ei4X8Lyc... sold 5.9406 SOL of h1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:20.670453Z"}
2025-05-26 13:20:20,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:20.702190Z"}
2025-05-26 13:20:22,563 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of 0xblastard", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:22.563080Z"}
2025-05-26 13:20:23,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:25,584 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: WRXFrF1p... sold 0.9901 SOL of NIC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:25.584197Z"}
2025-05-26 13:20:25,655 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3hwS51f1... sold 1.9802 SOL of firecoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:25.655844Z"}
2025-05-26 13:20:29,694 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gvdn5Znu... sold 0.9901 SOL of RIP K9 Ban", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:29.694222Z"}
2025-05-26 13:20:29,764 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GewBprxs... sold 5.0000 SOL of Roadster", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:29.764285Z"}
2025-05-26 13:20:33,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:36,950 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ZeU9Lbc... sold 3.0000 SOL of JAMES", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:36.950154Z"}
2025-05-26 13:20:41,608 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of GOONDAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:41.608504Z"}
2025-05-26 13:20:43,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:47,396 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 93dggmM2... sold 0.2475 SOL of FAGWHEEL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:47.396155Z"}
2025-05-26 13:20:51,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BjCcu5RH... sold 0.4987 SOL of WW3MUPPETS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:51.081998Z"}
2025-05-26 13:20:52,617 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9MusJmn6... sold 0.3960 SOL of Maya", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:52.616986Z"}
2025-05-26 13:20:53,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:57,730 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FBo4KX47... sold 0.5941 SOL of Argt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:57.730237Z"}
2025-05-26 13:21:02,380 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 4.9505 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:02.380411Z"}
2025-05-26 13:21:02,540 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of P33STUDiO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:02.540092Z"}
2025-05-26 13:21:03,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:09,198 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: JE754JqB... sold 0.1000 SOL of BRASIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:09.198284Z"}
2025-05-26 13:21:10,435 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dp1XWk5a... sold 1.1990 SOL of who", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:10.434832Z"}
2025-05-26 13:21:11,050 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ZUkJdSLE... sold 2.0000 SOL of \ud83e\udd90", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:11.050678Z"}
2025-05-26 13:21:14,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:15,048 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of bike", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:15.047907Z"}
2025-05-26 13:21:16,585 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4b1Xr46u... sold 0.1980 SOL of prison", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:16.585636Z"}
2025-05-26 13:21:24,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:25,179 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of DRAGAPEX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:25.179674Z"}
2025-05-26 13:21:28,559 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ANQvetBM... sold 1.9802 SOL of MAGNUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:28.558968Z"}
2025-05-26 13:21:32,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F4dmmtM7... sold 0.4257 SOL of BEACH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:32.339016Z"}
2025-05-26 13:21:32,477 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: JE754JqB... sold 0.1000 SOL of BRASIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:32.477714Z"}
2025-05-26 13:21:34,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:35,931 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fy3XcLYZ... sold 0.2970 SOL of \ud83d\ude80", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:35.931710Z"}
2025-05-26 13:21:36,239 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8rSeJJRZ... sold 0.0099 SOL of TESTF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:36.239480Z"}
2025-05-26 13:21:37,467 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DKuNKQed... sold 1.6337 SOL of CWB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:37.467402Z"}
2025-05-26 13:21:40,539 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: oNJ2nas9... sold 1.4851 SOL of hyperchill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:40.539554Z"}
2025-05-26 13:21:44,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:51,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 1.5446 SOL of ROSS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:51.102368Z"}
2025-05-26 13:21:52,214 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AqVnksyB... sold 0.9901 SOL of ELON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.214535Z"}
2025-05-26 13:21:52,257 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: B2CRi1hs... sold 0.1000 SOL of MAYA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.257713Z"}
2025-05-26 13:21:52,307 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 667AWcos... sold 2.9703 SOL of LUPIA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.307299Z"}
2025-05-26 13:21:54,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:56,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EoWwfq9N... sold 0.0010 SOL of botcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:56.019824Z"}
2025-05-26 13:21:57,435 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9sCcAxe5... sold 4.0000 SOL of WT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:57.435894Z"}
2025-05-26 13:21:59,279 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Hw5w1axG... sold 0.0593 SOL of SETH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:59.279147Z"}
2025-05-26 13:22:03,272 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APVEy4vU... sold 1.4851 SOL of STD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:03.272506Z"}
2025-05-26 13:22:04,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:09,330 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5uvXxXjL... sold 1.9000 SOL of BarbieGirl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:09.329117Z"}
2025-05-26 13:22:09,941 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EwyvPTKi... sold 1.1000 SOL of CryptoSwar", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:09.941377Z"}
2025-05-26 13:22:13,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of OggyStealS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:13.102257Z"}
2025-05-26 13:22:13,153 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7i6bfTP8... sold 1.4900 SOL of Cubikka", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:13.152943Z"}
2025-05-26 13:22:14,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:15,270 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of CRYPT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:15.270871Z"}
2025-05-26 13:22:15,994 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CrMSFMdW... sold 1.1881 SOL of TRRLO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:15.994479Z"}
2025-05-26 13:22:19,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DSVNmMZi... sold 0.9901 SOL of STARSHIP 3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:19.339578Z"}
2025-05-26 13:22:19,365 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 44nFNkKU... sold 0.9901 SOL of pgooner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:19.365412Z"}
2025-05-26 13:22:20,965 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AAPkNToN... sold 0.2077 SOL of moondog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:20.965896Z"}
2025-05-26 13:22:21,432 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2xKDETTh... sold 2.9703 SOL of RIP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:21.432453Z"}
2025-05-26 13:22:21,507 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FjeaKDFA... sold 2.9703 SOL of Mr Bean", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:21.507706Z"}
2025-05-26 13:22:24,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:25,579 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9maDquBn... sold 0.0297 SOL of LockN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:25.579458Z"}
2025-05-26 13:22:26,500 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: vGwtpGFM... sold 0.0010 SOL of CSR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:26.500638Z"}
2025-05-26 13:22:29,504 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 66666KoH... sold 1.9802 SOL of Mr Bean", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:29.504817Z"}
2025-05-26 13:22:34,341 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HM11w5fe... sold 0.1188 SOL of squid", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:34.341679Z"}
2025-05-26 13:22:35,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:36,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9r8NuiwA... sold 3.8565 SOL of SUPR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:36.022516Z"}
2025-05-26 13:22:36,944 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2cJN9h83... sold 1.6000 SOL of ButtHurt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:36.943918Z"}
2025-05-26 13:22:38,508 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GGbQBvbq... sold 0.8911 SOL of LR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:38.508559Z"}
2025-05-26 13:22:43,239 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoLTdQey... sold 0.9802 SOL of 59s", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:43.239162Z"}
2025-05-26 13:22:43,295 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2Zobi8ji... sold 0.1980 SOL of fly", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:43.295651Z"}
2025-05-26 13:22:45,076 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of IA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:45.075955Z"}
2025-05-26 13:22:45,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:45,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of TRUMPACCTS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:45.430330Z"}
2025-05-26 13:22:54,453 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVWP2wwf... sold 3.0000 SOL of send", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:54.452980Z"}
2025-05-26 13:22:55,202 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 723CcrTR... sold 0.7733 SOL of MOG16", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:55.202795Z"}
2025-05-26 13:22:55,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:57,821 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AV4zBZSe... sold 1.5842 SOL of MR BEAN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:57.821671Z"}
2025-05-26 13:22:59,060 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FaPWKVMi... sold 1.0891 SOL of PEPLUSH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:59.060775Z"}
2025-05-26 13:23:03,593 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8R7Do5RU... sold 0.0079 SOL of BTC BOOM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:23:03.593473Z"}
2025-05-26 13:23:04,460 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of TIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:23:04.460403Z"}
2025-05-26 18:25:46,310 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:46.310068Z"}
2025-05-26 18:25:46,658 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T22:25:46.658894Z"}
2025-05-26 18:25:46,734 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:46.733956Z"}
2025-05-26 18:25:46,744 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:46.744393Z"}
2025-05-26 18:25:46,751 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T22:25:46.751155Z"}
2025-05-26 18:25:46,755 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:25:46.755391Z"}
2025-05-26 18:25:47,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 18:25:47,410 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:25:47.410006Z"}
2025-05-26 18:25:47,416 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.416797Z"}
2025-05-26 18:25:47,426 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.426166Z"}
2025-05-26 18:25:47,477 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.477329Z"}
2025-05-26 18:25:47,509 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.509431Z"}
2025-05-26 18:25:47,512 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.512572Z"}
2025-05-26 18:25:47,529 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.528945Z"}
2025-05-26 18:25:47,555 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.554792Z"}
2025-05-26 18:25:47,577 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.577598Z"}
2025-05-26 18:25:47,678 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T22:25:47.678112Z"}
2025-05-26 18:25:47,690 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T22:25:47.689858Z"}
2025-05-26 18:25:47,726 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:47.726101Z"}
2025-05-26 18:25:49,575 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.575734Z"}
2025-05-26 18:25:49,609 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.596621Z"}
2025-05-26 18:25:49,695 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.695365Z"}
2025-05-26 18:25:49,711 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.711708Z"}
2025-05-26 18:25:49,757 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T22:25:49.757630Z"}
2025-05-26 18:25:49,767 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:49.767085Z"}
2025-05-26 18:25:50,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 18:25:50,025 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 18:25:50,028 - telegram.ext.Application - INFO - Application started
2025-05-26 18:25:50,051 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:50.051145Z"}
2025-05-26 18:25:50,076 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:50.075216Z"}
2025-05-26 18:25:50,146 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 18:25:50,157 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:25:50.157451Z"}
2025-05-26 18:25:50,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:25:50,706 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:25:50,710 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:25:50,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:25:51,028 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:25:54,716 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4LVFuib4... sold 1.9802 SOL of PEPECASH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:54.716642Z"}
2025-05-26 18:25:56,497 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ruvDYrD... sold 1.9802 SOL of IPO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:56.497021Z"}
2025-05-26 18:25:57,926 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G3mVExWm... sold 0.4950 SOL of Hodl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:57.925859Z"}
2025-05-26 18:25:59,595 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ry6xipR... sold 1.6864 SOL of TODA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:59.595369Z"}
2025-05-26 18:25:59,768 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ocuGGEC... sold 1.4663 SOL of TRENCHIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:59.767949Z"}
2025-05-26 18:26:00,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:04,457 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4vejd1ry... sold 1.4851 SOL of FULLPORT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:04.457761Z"}
2025-05-26 18:26:06,835 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of grenboi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:06.835296Z"}
2025-05-26 18:26:06,953 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AfKqD4nZ... sold 1.6864 SOL of TODA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:06.952997Z"}
2025-05-26 18:26:10,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:12,667 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3NMn55Rx... sold 1.8812 SOL of shit", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:12.667439Z"}
2025-05-26 18:26:13,588 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.9703 SOL of DV3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:13.588804Z"}
2025-05-26 18:26:14,719 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 49nSpmxw... sold 1.0891 SOL of Tanner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:14.719435Z"}
2025-05-26 18:26:18,723 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gt5t3J1x... sold 1.4851 SOL of COW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:18.723126Z"}
2025-05-26 18:26:18,775 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: MFznJg5c... sold 1.9957 SOL of Tanner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:18.775436Z"}
2025-05-26 18:26:18,939 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EMeKZkQJ... sold 1.0000 SOL of MPW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:18.939025Z"}
2025-05-26 18:26:19,737 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GxjBNaQc... sold 0.9901 SOL of INFLUENCE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:19.737723Z"}
2025-05-26 18:26:19,808 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6UwHzKQP... sold 1.0000 SOL of CryptoCons", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:19.808810Z"}
2025-05-26 18:26:20,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:25,145 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Db1g8u2n... sold 7.9208 SOL of GMI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:25.145258Z"}
2025-05-26 18:26:27,879 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AD1mB49Q... sold 0.0500 SOL of RND1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:27.879451Z"}
2025-05-26 18:26:31,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:32,322 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of ANONYM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:32.322814Z"}
2025-05-26 18:26:33,201 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of NOICE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:33.201771Z"}
2025-05-26 18:26:33,676 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FBdKJMmM... sold 2.9703 SOL of SKILL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:33.676559Z"}
2025-05-26 18:26:36,627 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 65mCJfTf... sold 2.0000 SOL of $NAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:36.626997Z"}
2025-05-26 18:26:38,028 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6dqfFhtF... sold 1.2000 SOL of BRAIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:38.028483Z"}
2025-05-26 18:26:41,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:48,915 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6diAtT1i... sold 3.0000 SOL of Censored", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:48.915205Z"}
2025-05-26 18:26:49,139 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8PR9gJow... sold 0.7921 SOL of orangie", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:49.138980Z"}
2025-05-26 18:26:49,836 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4LVFuib4... sold 1.9802 SOL of TRUMPCOIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:49.836510Z"}
2025-05-26 18:26:49,966 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BLm4RRwc... sold 2.0000 SOL of PVP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:49.966175Z"}
2025-05-26 18:26:51,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:52,603 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HFPa983C... sold 0.4950 SOL of CROW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:52.602809Z"}
2025-05-26 18:26:53,796 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EWPefkYv... sold 0.9901 SOL of OneCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:53.796047Z"}
2025-05-26 18:26:57,830 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of MANIFEST", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:57.827682Z"}
2025-05-26 18:26:57,971 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9bVND3C8... sold 0.9901 SOL of Lambobu", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:57.971099Z"}
2025-05-26 18:26:58,452 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4eG3pbV6... sold 0.0297 SOL of NEWPORT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:58.452508Z"}
2025-05-26 18:27:01,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:02,188 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: A8KaH4NE... sold 1.2871 SOL of LORE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:02.188774Z"}
2025-05-26 18:27:03,966 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of SelamiSeyi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:03.966840Z"}
2025-05-26 18:27:04,242 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4evdt6SS... sold 3.0000 SOL of MANTIS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:04.241955Z"}
2025-05-26 18:27:05,321 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of brok", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:05.312734Z"}
2025-05-26 18:27:06,127 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AsMwZDHv... sold 1.0000 SOL of KANSEI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:06.127040Z"}
2025-05-26 18:27:08,575 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:09,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:27:10,129 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9TNtkdDK... sold 5.0000 SOL of MONGO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:10.129408Z"}
2025-05-26 18:27:11,645 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HKQ2m2Z2... sold 1.9802 SOL of bratz", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:11.645085Z"}
2025-05-26 18:27:14,309 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: YKsdAJqh... sold 2.8500 SOL of KAWS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:14.309692Z"}
2025-05-26 18:27:17,059 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CU6NSebF... sold 0.9901 SOL of RlGETTl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:17.059547Z"}
2025-05-26 18:27:18,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:18,818 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Ef2TCv2k... sold 1.9000 SOL of Tanner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:18.818390Z"}
2025-05-26 18:27:19,631 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of TOPBLAST", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:19.631728Z"}
2025-05-26 18:27:21,717 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4mR8nMmb... sold 1.9802 SOL of BitDog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:21.717306Z"}
2025-05-26 18:27:25,324 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DVmcDrci... sold 3.0000 SOL of MONGO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:25.324764Z"}
2025-05-26 18:27:25,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:25,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 18:27:26,696 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9RWzqUzs... sold 1.9802 SOL of gotcha", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:26.696061Z"}
2025-05-26 18:27:29,325 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3Tx9rimP... sold 1.9802 SOL of DarkCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:29.325111Z"}
2025-05-26 18:27:31,418 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3K8HCjxV... sold 0.9901 SOL of VEIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:31.418285Z"}
2025-05-26 18:27:32,816 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BS5bp77G... sold 1.2871 SOL of SDERIV", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:32.811589Z"}
2025-05-26 18:27:35,124 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6YFaXy5a... sold 0.2970 SOL of NVIDIA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:35.124015Z"}
2025-05-26 18:27:35,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:37,107 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CM3KxcCz... sold 1.1881 SOL of Loveski", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:37.107864Z"}
2025-05-26 18:27:37,763 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CSL9Cab3... sold 1.9957 SOL of bratz", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:37.763266Z"}
2025-05-26 18:27:39,290 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3Rb64K2v... sold 4.9307 SOL of RDOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:39.290167Z"}
2025-05-26 18:27:45,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HpmiTLon... sold 2.0000 SOL of OWKEE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:45.082014Z"}
2025-05-26 18:27:45,129 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: XDpDXxD5... sold 1.9802 SOL of Hand", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:45.129039Z"}
2025-05-26 18:27:45,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:48,516 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of PHYSICIST", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:48.516819Z"}
2025-05-26 18:27:51,751 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ATRn826L... sold 0.9900 SOL of PANCAKE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:51.751120Z"}
2025-05-26 18:27:53,520 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fxgd9QHS... sold 0.9901 SOL of trollcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:53.520077Z"}
2025-05-26 18:27:53,666 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: D4V41jWV... sold 0.0297 SOL of KERMIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:53.666626Z"}
2025-05-26 18:27:55,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:55,897 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2hTeVuXA... sold 0.0990 SOL of BUBUTARD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:55.897226Z"}
2025-05-26 18:27:55,989 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Dj1cUYo... sold 1.0000 SOL of orange", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:55.989178Z"}
2025-05-26 18:27:56,560 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BLm4RRwc... sold 2.0000 SOL of mememart", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:56.559810Z"}
2025-05-26 18:28:01,408 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoGxGZ5y... sold 1.9986 SOL of MHGA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:01.408825Z"}
2025-05-26 18:28:03,250 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EJyP6A4t... sold 1.9802 SOL of Lightning", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:03.250677Z"}
2025-05-26 18:28:03,662 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZ85GFTe... sold 1.4940 SOL of Pooper", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:03.661895Z"}
2025-05-26 18:28:06,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:07,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:08,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:28:08,493 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: uDv4YUKA... sold 1.7822 SOL of TIME", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:08.493376Z"}
2025-05-26 18:28:09,393 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of LOLA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:09.393622Z"}
2025-05-26 18:28:09,466 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 36DWP52M... sold 8.9000 SOL of lola", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:09.466288Z"}
2025-05-26 18:28:10,463 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cWkmK6v... sold 0.9901 SOL of Librium", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:10.463567Z"}
2025-05-26 18:28:10,521 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F1kPoBCx... sold 0.9900 SOL of DEER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:10.521379Z"}
2025-05-26 18:28:10,927 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 83QQFLxc... sold 13.0000 SOL of lola", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:10.927852Z"}
2025-05-26 18:28:11,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:11,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 18:28:11,597 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/editMessageText "HTTP/1.1 200 OK"
2025-05-26 18:28:14,150 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EBbQ8XAa... sold 3.0000 SOL of LOLA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:14.149982Z"}
2025-05-26 18:28:14,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:14,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:28:20,926 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3r9itQab... sold 0.9901 SOL of HOLD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:20.926734Z"}
2025-05-26 18:28:21,373 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 35vxt3dg... sold 1.2376 SOL of SEAHAVEN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:21.373735Z"}
2025-05-26 18:28:24,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:26,161 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 1.4851 SOL of 1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:26.161684Z"}
2025-05-26 18:28:30,246 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9W9JMJ71... sold 0.9594 SOL of DEBUBU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:30.246532Z"}
2025-05-26 18:28:31,818 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F64s8Agh... sold 0.9901 SOL of bsp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:31.818239Z"}
2025-05-26 18:28:34,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:39,803 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EuD93tAE... sold 1.4663 SOL of BAGIFY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:39.803314Z"}
2025-05-26 18:28:41,142 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DvM4WgfY... sold 1.1881 SOL of DaddyFrog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:41.141944Z"}
2025-05-26 18:28:41,378 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EMciuCx4... sold 2.5743 SOL of airedex", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:41.378617Z"}
2025-05-26 18:28:43,684 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of LOLA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:43.684504Z"}
2025-05-26 18:28:44,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:48,397 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of PC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:48.396887Z"}
2025-05-26 18:28:51,946 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: mtaDFhB9... sold 0.6931 SOL of MONSTER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:51.946688Z"}
2025-05-26 18:28:53,934 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3J9fPWHy... sold 0.1287 SOL of 9TO5", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:53.934136Z"}
2025-05-26 18:28:54,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:57,005 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmNMqM5V... sold 1.9802 SOL of Dandy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:57.005020Z"}
2025-05-26 18:28:57,926 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4evdt6SS... sold 3.0000 SOL of MANTIS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:57.926562Z"}
2025-05-26 18:29:02,156 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2TTzNPtH... sold 0.1980 SOL of protogen", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:29:02.156280Z"}
2025-05-26 18:29:02,539 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AzbKWG1b... sold 0.0495 SOL of Skeetcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:29:02.539823Z"}
2025-05-26 18:31:42,632 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:42.632312Z"}
2025-05-26 18:31:42,836 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T22:31:42.835666Z"}
2025-05-26 18:31:42,844 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:42.843978Z"}
2025-05-26 18:31:42,867 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:42.864840Z"}
2025-05-26 18:31:42,872 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T22:31:42.871567Z"}
2025-05-26 18:31:42,878 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:31:42.878709Z"}
2025-05-26 18:31:43,537 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 18:31:43,546 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:31:43.546145Z"}
2025-05-26 18:31:43,548 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.548441Z"}
2025-05-26 18:31:43,567 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.567790Z"}
2025-05-26 18:31:43,610 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.610109Z"}
2025-05-26 18:31:43,646 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.646057Z"}
2025-05-26 18:31:43,734 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.733963Z"}
2025-05-26 18:31:43,773 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.773145Z"}
2025-05-26 18:31:43,801 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.800062Z"}
2025-05-26 18:31:43,809 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.808804Z"}
2025-05-26 18:31:43,861 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T22:31:43.861149Z"}
2025-05-26 18:31:43,878 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T22:31:43.878226Z"}
2025-05-26 18:31:43,932 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:43.932167Z"}
2025-05-26 18:31:43,958 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.957576Z"}
2025-05-26 18:31:43,996 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.995932Z"}
2025-05-26 18:31:44,003 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:44.003788Z"}
2025-05-26 18:31:44,014 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:44.014465Z"}
2025-05-26 18:31:44,023 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T22:31:44.023479Z"}
2025-05-26 18:31:44,031 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.030807Z"}
2025-05-26 18:31:44,207 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.206964Z"}
2025-05-26 18:31:44,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 18:31:44,319 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 18:31:44,339 - telegram.ext.Application - INFO - Application started
2025-05-26 18:31:44,364 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.364714Z"}
2025-05-26 18:31:44,370 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fys5tije... sold 2.9703 SOL of 3e4r4tghy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.370456Z"}
2025-05-26 18:31:44,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 18:31:44,577 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:31:44.576255Z"}
2025-05-26 18:31:44,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:31:45,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:31:45,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:31:45,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:31:45,779 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:31:46,079 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of MACA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:46.079304Z"}
2025-05-26 18:31:46,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:31:46,146 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:31:48,710 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6EgND3B8... sold 0.9600 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:48.710375Z"}
2025-05-26 18:31:52,708 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6RmPfs3k... sold 2.0000 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:52.708518Z"}
2025-05-26 18:31:55,124 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:31:56,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DD7BGECk... sold 0.2574 SOL of 100mil", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:56.702041Z"}
2025-05-26 18:31:58,237 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2vYTNjhP... sold 4.8000 SOL of MACA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:58.237295Z"}
2025-05-26 18:31:59,098 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C2gw4Vza... sold 1.6000 SOL of CALC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:59.098640Z"}
2025-05-26 18:31:59,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F1M3fncB... sold 0.9700 SOL of Bink", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:59.472986Z"}
2025-05-26 18:32:04,178 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5r7c2MHp... sold 1.9802 SOL of LUCKY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:04.178321Z"}
2025-05-26 18:32:04,679 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DBHhBFV6... sold 0.9901 SOL of Giraffe", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:04.679157Z"}
2025-05-26 18:32:05,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:08,285 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of BIGBANG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:08.285604Z"}
2025-05-26 18:32:08,379 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FHkvAeEv... sold 2.0000 SOL of GUWEIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:08.379297Z"}
2025-05-26 18:32:10,218 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5r1HzfMn... sold 1.9802 SOL of frok", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:10.218231Z"}
2025-05-26 18:32:13,466 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:13.466292Z"}
2025-05-26 18:32:13,494 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmNMqM5V... sold 1.9802 SOL of Greeny", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:13.494359Z"}
2025-05-26 18:32:14,454 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: cartidEt... sold 15.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:14.454295Z"}
2025-05-26 18:32:14,664 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7GaE86n5... sold 13.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:14.664347Z"}
2025-05-26 18:32:15,048 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BwaVFCDJ... sold 10.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.048286Z"}
2025-05-26 18:32:15,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:15,444 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: dev6SDTV... sold 0.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.444256Z"}
2025-05-26 18:32:15,660 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7GhWwhaM... sold 15.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.660043Z"}
2025-05-26 18:32:15,857 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: iSbXfAXf... sold 1.4920 SOL of caaaaaaaat", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.856969Z"}
2025-05-26 18:32:16,845 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoGxGZ5y... sold 1.9986 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:16.844977Z"}
2025-05-26 18:32:17,093 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 36DWP52M... sold 8.9000 SOL of rip sammy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:17.092955Z"}
2025-05-26 18:32:17,672 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2vYTNjhP... sold 4.8000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:17.672324Z"}
2025-05-26 18:32:20,354 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Efd2Zc5m... sold 3.0115 SOL of LTC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:20.354846Z"}
2025-05-26 18:32:20,407 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DuWQpuU3... sold 1.9000 SOL of SELL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:20.407213Z"}
2025-05-26 18:32:20,445 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9LFLuhNa... sold 1.6864 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:20.445348Z"}
2025-05-26 18:32:21,278 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7GaE86n5... sold 13.0000 SOL of RS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:21.278218Z"}
2025-05-26 18:32:21,334 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2dyDktKn... sold 1.4000 SOL of Chain|Mag", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:21.333820Z"}
2025-05-26 18:32:25,471 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: En5AaBtu... sold 1.9802 SOL of Lovot", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:25.471120Z"}
2025-05-26 18:32:25,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:26,499 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FrJoNhzV... sold 2.0000 SOL of king DuKE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:26.499751Z"}
2025-05-26 18:32:27,115 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: dev6SDTV... sold 0.0000 SOL of RS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:27.115602Z"}
2025-05-26 18:32:28,266 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3mtADs86... sold 0.9337 SOL of wigi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:28.265926Z"}
2025-05-26 18:32:28,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:28,399 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DBg4BnH6... sold 1.2871 SOL of MurG-AI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:28.399029Z"}
2025-05-26 18:32:28,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:32:30,797 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of Samuel", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:30.797557Z"}
2025-05-26 18:32:31,707 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CcVhzDkr... sold 1.9000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:31.707209Z"}
2025-05-26 18:32:32,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:32,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 18:32:33,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/editMessageText "HTTP/1.1 200 OK"
2025-05-26 18:32:33,610 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: dev6SDTV... sold 0.0000 SOL of RIPSAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:33.610637Z"}
2025-05-26 18:32:34,075 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5xFdEbnG... sold 0.9000 SOL of Uprising", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:34.075614Z"}
2025-05-26 18:32:37,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GFA2t9fZ... sold 2.9703 SOL of CASECLOSED", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:37.240999Z"}
2025-05-26 18:32:40,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EzeGwS6W... sold 0.9495 SOL of Drill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:40.102333Z"}
2025-05-26 18:32:42,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:43,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:43,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:32:44,590 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G7NvZKjo... sold 1.9802 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:44.590591Z"}
2025-05-26 18:32:44,734 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3mQvUx8J... sold 1.6864 SOL of RS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:44.734489Z"}
2025-05-26 18:32:44,817 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2nyUSzHR... sold 3.0000 SOL of pepzard", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:44.817099Z"}
2025-05-26 18:32:48,701 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 1.9802 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:48.701071Z"}
2025-05-26 18:32:48,815 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HY4Sek2U... sold 0.0099 SOL of BUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:48.813582Z"}
2025-05-26 18:32:49,425 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9TNtkdDK... sold 5.0000 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:49.425400Z"}
2025-05-26 18:32:49,622 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:49.622910Z"}
2025-05-26 18:32:52,300 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DR7ghPiR... sold 2.4752 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:52.300247Z"}
2025-05-26 18:32:52,330 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of THREE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:52.329921Z"}
2025-05-26 18:32:53,234 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GxjBNaQc... sold 0.9901 SOL of Exposure", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:53.233455Z"}
2025-05-26 18:32:53,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:53,712 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: mtaDFhB9... sold 0.4950 SOL of MONSTER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:53.712125Z"}
2025-05-26 18:32:57,224 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5fMuWkZB... sold 1.9000 SOL of limitless", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:57.224392Z"}
2025-05-26 18:33:16,732 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:16.732194Z"}
2025-05-26 18:33:16,902 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T22:33:16.902552Z"}
2025-05-26 18:33:16,906 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:16.906095Z"}
2025-05-26 18:33:16,911 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:16.910976Z"}
2025-05-26 18:33:16,920 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T22:33:16.920733Z"}
2025-05-26 18:33:16,936 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:33:16.936846Z"}
2025-05-26 18:33:17,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 18:33:17,603 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:33:17.602373Z"}
2025-05-26 18:33:17,612 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.612035Z"}
2025-05-26 18:33:17,618 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.618006Z"}
2025-05-26 18:33:17,623 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.622726Z"}
2025-05-26 18:33:17,630 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.630712Z"}
2025-05-26 18:33:17,634 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.634721Z"}
2025-05-26 18:33:17,638 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.638310Z"}
2025-05-26 18:33:17,647 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.647413Z"}
2025-05-26 18:33:17,653 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.653057Z"}
2025-05-26 18:33:17,680 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T22:33:17.680592Z"}
2025-05-26 18:33:17,693 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T22:33:17.693209Z"}
2025-05-26 18:33:17,701 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:17.701086Z"}
2025-05-26 18:33:17,705 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.705421Z"}
2025-05-26 18:33:17,714 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.713983Z"}
2025-05-26 18:33:17,724 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.724371Z"}
2025-05-26 18:33:17,735 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.735218Z"}
2025-05-26 18:33:17,749 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T22:33:17.749423Z"}
2025-05-26 18:33:17,755 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:17.755605Z"}
2025-05-26 18:33:18,036 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:18.036022Z"}
2025-05-26 18:33:18,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 18:33:18,064 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 18:33:18,099 - telegram.ext.Application - INFO - Application started
2025-05-26 18:33:18,188 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:18.188242Z"}
2025-05-26 18:33:18,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 18:33:18,282 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:33:18.282505Z"}
2025-05-26 18:33:18,496 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EuD93tAE... sold 3.0832 SOL of BAGIFY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:18.496146Z"}
2025-05-26 18:33:21,549 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.9703 SOL of 200PUMPED", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:21.549106Z"}
2025-05-26 18:33:25,163 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5vAsUjJn... sold 2.4752 SOL of csdpae", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:25.163018Z"}
2025-05-26 18:33:27,010 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3NMn55Rx... sold 1.7822 SOL of Bounce", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:27.010551Z"}
2025-05-26 18:33:27,707 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4o3yQZU6... sold 2.5743 SOL of Sammy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:27.707806Z"}
2025-05-26 18:33:27,868 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6HfntwEB... sold 1.9802 SOL of Conspiracy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:27.868368Z"}
2025-05-26 18:33:28,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:30,698 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7eiGgyRs... sold 1.9802 SOL of Benny ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:30.698164Z"}
2025-05-26 18:33:30,771 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of TICKER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:30.770973Z"}
2025-05-26 18:33:32,154 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9TNtkdDK... sold 5.0000 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:32.154180Z"}
2025-05-26 18:33:32,911 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ErNwtzH2... sold 2.9703 SOL of retardcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:32.911195Z"}
2025-05-26 18:33:33,319 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GGdxDu7y... sold 1.9345 SOL of DERP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:33.319403Z"}
2025-05-26 18:33:34,383 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of BOT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:34.383576Z"}
2025-05-26 18:33:39,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:39,141 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 71PA9zV8... sold 0.9901 SOL of dvces", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:39.141217Z"}
2025-05-26 18:33:39,605 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DR7ghPiR... sold 2.4752 SOL of RIP SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:39.605415Z"}
2025-05-26 18:33:43,905 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CJdc9VpW... sold 2.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:43.905092Z"}
2025-05-26 18:33:47,369 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5Mj2GV5Z... sold 1.9802 SOL of $Panda", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:47.369459Z"}
2025-05-26 18:33:48,413 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 56d6rLmS... sold 0.9901 SOL of RNC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:48.412977Z"}
2025-05-26 18:33:48,466 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AzVeJaeS... sold 0.0792 SOL of aaqa", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:48.466875Z"}
2025-05-26 18:33:49,216 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:49,600 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmNMqM5V... sold 1.9802 SOL of ZUCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:49.600161Z"}
2025-05-26 18:33:50,566 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6irpf6LR... sold 0.6634 SOL of selfmade", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:50.564598Z"}
2025-05-26 18:33:54,565 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of yuge", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:54.565385Z"}
2025-05-26 18:33:58,089 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of BOT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:58.088990Z"}
2025-05-26 18:33:58,649 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Byonzktg... sold 0.0090 SOL of INFINITE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:58.648977Z"}
2025-05-26 18:33:58,815 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Eh8utmrD... sold 2.9703 SOL of boomer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:58.815324Z"}
2025-05-26 18:33:59,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:59,743 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4vejd1ry... sold 1.4851 SOL of challenge", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:59.742795Z"}
2025-05-26 18:34:02,951 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8AXFkxT8... sold 1.9802 SOL of PLAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:02.950598Z"}
2025-05-26 18:34:03,030 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GxjBNaQc... sold 0.9901 SOL of \ud83d\udd25", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:03.029953Z"}
2025-05-26 18:34:04,683 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DFe9ud8i... sold 1.6864 SOL of boomer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:04.683520Z"}
2025-05-26 18:34:05,161 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7AJFzqaB... sold 1.9802 SOL of Sandwich", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:05.161067Z"}
2025-05-26 18:34:09,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:34:15,475 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gnn6JcAe... sold 1.9802 SOL of McKOL's", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:15.474828Z"}
2025-05-26 18:34:15,548 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5r1HzfMn... sold 1.9802 SOL of god", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:15.548431Z"}
2025-05-26 18:34:18,300 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Dj1cUYo... sold 0.9901 SOL of gmail", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:18.300565Z"}
2025-05-26 18:34:19,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:34:21,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 82eYQw9f... sold 1.6864 SOL of boomer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:21.381178Z"}
2025-05-26 18:34:22,626 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5w6Le7UN... sold 1.9000 SOL of Sandwich", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:22.626750Z"}
2025-05-26 18:34:22,835 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ADC1QV9r... sold 1.9802 SOL of O-SCIENCE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:22.835585Z"}
2025-05-26 18:34:24,069 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: E4zV6o7Y... sold 1.9000 SOL of ENEG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:24.069552Z"}
2025-05-26 18:34:27,216 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HTpnNqP1... sold 3.0000 SOL of Explode", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:27.216450Z"}
2025-05-26 18:34:27,258 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4mphVxSw... sold 1.2000 SOL of Crypto", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:27.258657Z"}
2025-05-26 18:34:29,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:34:29,691 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of seed", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:29.691479Z"}
2025-05-26 18:34:31,356 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dr8Ej9Vd... sold 1.1881 SOL of koko", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:31.356694Z"}
2025-05-26 18:34:31,684 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CEUA7zVo... sold 1.7822 SOL of Tension", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:31.684576Z"}
2025-05-26 18:34:31,734 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6x13RjmW... sold 2.9703 SOL of 4", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:31.734743Z"}
2025-05-26 18:34:32,786 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5Yderdf4... sold 0.0010 SOL of FUNNYS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:32.786440Z"}
