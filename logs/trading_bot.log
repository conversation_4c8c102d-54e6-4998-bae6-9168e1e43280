2025-05-25 17:54:55,737 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:55.736854Z"}
2025-05-25 17:54:56,397 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:54:56.397454Z"}
2025-05-25 17:54:56,403 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.403735Z"}
2025-05-25 17:54:56,413 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.412323Z"}
2025-05-25 17:54:56,447 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:54:56.447070Z"}
2025-05-25 17:54:56,487 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:54:56.487287Z"}
2025-05-25 17:54:56,498 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:54:56.498359Z"}
2025-05-25 17:54:56,516 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:54:56.516584Z"}
2025-05-25 17:54:56,528 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:54:56.528819Z"}
2025-05-25 17:54:56,537 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.537572Z"}
2025-05-25 17:54:56,543 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:54:56.543847Z"}
2025-05-25 17:54:56,550 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.550749Z"}
2025-05-25 17:54:56,560 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.560229Z"}
2025-05-25 17:54:56,566 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:54:56.566354Z"}
2025-05-25 17:54:56,573 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.572971Z"}
2025-05-25 17:56:10,941 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:10.941571Z"}
2025-05-25 17:56:11,118 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:56:11.118560Z"}
2025-05-25 17:56:11,124 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.124063Z"}
2025-05-25 17:56:11,129 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.129341Z"}
2025-05-25 17:56:11,135 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:56:11.135574Z"}
2025-05-25 17:56:11,150 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:56:11.150751Z"}
2025-05-25 17:56:11,191 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:56:11.191162Z"}
2025-05-25 17:56:11,219 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:56:11.219027Z"}
2025-05-25 17:56:11,235 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:56:11.235235Z"}
2025-05-25 17:56:11,248 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.248662Z"}
2025-05-25 17:56:11,276 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:56:11.276372Z"}
2025-05-25 17:56:11,289 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.289307Z"}
2025-05-25 17:56:11,301 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.301738Z"}
2025-05-25 17:56:11,311 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:56:11.311540Z"}
2025-05-25 17:56:11,315 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.315073Z"}
2025-05-25 17:57:35,285 - test_bot - INFO - {"event": "\u23f0 Test started at: 2025-05-25 17:57:35", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.285544Z"}
2025-05-25 17:57:35,294 - test_bot - INFO - {"event": "\ud83d\ude80 Starting Minimal Solana Trading Bot Test...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.293997Z"}
2025-05-25 17:57:35,298 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.298890Z"}
2025-05-25 17:57:35,318 - test_bot - INFO - {"event": "\ud83d\udcca Testing database connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.318141Z"}
2025-05-25 17:57:35,423 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:57:35.423852Z"}
2025-05-25 17:57:35,426 - test_bot - INFO - {"event": "\u2705 Database initialized successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.426162Z"}
2025-05-25 17:57:35,428 - test_bot - INFO - {"event": "\u2699\ufe0f Testing configuration...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.428409Z"}
2025-05-25 17:57:35,439 - test_bot - INFO - {"event": "RPC URL: https://api.mainnet-beta.solana.com", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.439508Z"}
2025-05-25 17:57:35,441 - test_bot - INFO - {"event": "Pump.fun monitoring: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.441886Z"}
2025-05-25 17:57:35,444 - test_bot - INFO - {"event": "Copy trading: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.444079Z"}
2025-05-25 17:57:35,476 - test_bot - INFO - {"event": "Telegram bot: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.476631Z"}
2025-05-25 17:57:35,479 - test_bot - INFO - {"event": "\u2705 Configuration loaded successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.478979Z"}
2025-05-25 17:57:35,489 - test_bot - INFO - {"event": "\ud83d\ude80 Testing Pump.fun API connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.489327Z"}
2025-05-25 17:57:35,493 - test_bot - ERROR - {"event": "\u274c Pump.fun API test failed: 'PumpFunMonitor' object has no attribute 'initialize'", "logger": "test_bot", "level": "error", "timestamp": "2025-05-25T21:57:35.493535Z"}
2025-05-25 17:57:35,510 - test_bot - INFO - {"event": "\u23f1\ufe0f Running monitoring test for 30 seconds...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.509941Z"}
2025-05-25 17:57:35,519 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:57:35.519199Z"}
2025-05-25 17:57:35,670 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:35.669959Z"}
2025-05-25 17:57:40,754 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:40.754005Z"}
2025-05-25 17:57:45,838 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:45.838399Z"}
2025-05-25 17:57:50,923 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:50.923363Z"}
2025-05-25 17:57:56,042 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:56.042403Z"}
2025-05-25 17:58:01,133 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:58:01.133000Z"}
2025-05-25 17:58:05,521 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.521289Z"}
2025-05-25 17:58:05,524 - test_bot - INFO - {"event": "\u2705 Monitoring test completed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.523943Z"}
2025-05-25 17:58:05,526 - test_bot - INFO - {"event": "\ud83e\uddf9 Cleaning up...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.526420Z"}
2025-05-25 17:58:05,541 - test_bot - INFO - {"event": "\u2705 Database connections closed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.541837Z"}
2025-05-25 17:58:05,555 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.555592Z"}
2025-05-25 17:58:05,557 - test_bot - INFO - {"event": "\ud83c\udf89 All core functionality tests passed!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.557839Z"}
2025-05-25 17:58:05,568 - test_bot - INFO - {"event": "\u2705 The Solana Trading Bot core is working correctly", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.563329Z"}
2025-05-25 17:58:05,577 - test_bot - INFO - {"event": "\n\ud83d\ude80 CORE FUNCTIONALITY TEST: PASSED", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.577430Z"}
2025-05-25 17:58:05,591 - test_bot - INFO - {"event": "The bot's core systems are working correctly!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.591562Z"}
2025-05-25 17:58:05,623 - test_bot - INFO - {"event": "You can now run the full bot with: python run_bot.py", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.623162Z"}
2025-05-25 17:58:05,625 - test_bot - INFO - {"event": "\u23f0 Test completed at: 2025-05-25 17:58:05", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.625613Z"}
2025-05-25 17:58:05,640 - PumpFunMonitor - INFO - {"event": "New token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.640499Z"}
2025-05-25 17:58:05,652 - PumpFunMonitor - INFO - {"event": "Trending token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.652611Z"}
2025-05-25 17:58:05,658 - PumpFunMonitor - INFO - {"event": "Cleanup task cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.658466Z"}
2025-05-25 18:46:31,456 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.456573Z"}
2025-05-25 18:46:31,622 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:46:31.622346Z"}
2025-05-25 18:46:31,625 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.625427Z"}
2025-05-25 18:46:31,627 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.627806Z"}
2025-05-25 18:46:31,630 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:46:31.630520Z"}
2025-05-25 18:46:31,655 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T22:46:31.655722Z"}
2025-05-25 18:46:31,663 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T22:46:31.663001Z"}
2025-05-25 18:46:31,742 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T22:46:31.741884Z"}
2025-05-25 18:46:31,774 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T22:46:31.763601Z"}
2025-05-25 18:46:31,781 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.781163Z"}
2025-05-25 18:46:31,820 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T22:46:31.820381Z"}
2025-05-25 18:46:31,869 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.869355Z"}
2025-05-25 18:46:31,894 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.894049Z"}
2025-05-25 18:46:31,905 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T22:46:31.905015Z"}
2025-05-25 18:46:31,935 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.935541Z"}
2025-05-25 18:47:38,973 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.973327Z"}
2025-05-25 18:47:38,993 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.993781Z"}
2025-05-25 18:47:39,023 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.023435Z"}
2025-05-25 18:47:39,145 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:47:39.145619Z"}
2025-05-25 18:47:39,158 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.158521Z"}
2025-05-25 18:47:39,163 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.163394Z"}
2025-05-25 18:47:39,177 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:47:39.177044Z"}
2025-05-25 18:47:39,209 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.209601Z"}
2025-05-25 18:47:39,228 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.228144Z"}
2025-05-25 18:47:39,288 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.288079Z"}
2025-05-25 18:47:39,296 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.296179Z"}
2025-05-25 18:47:39,326 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.326022Z"}
2025-05-25 18:47:39,391 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.380255Z"}
2025-05-25 18:47:39,613 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:47:39.613500Z"}
2025-05-25 18:47:39,623 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:47:39.623665Z"}
2025-05-25 18:47:39,628 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:47:39.628676Z"}
2025-05-25 18:47:39,639 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.639707Z"}
2025-05-25 18:47:39,647 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.646932Z"}
2025-05-25 18:47:39,658 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.658237Z"}
2025-05-25 18:47:39,665 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.665154Z"}
2025-05-25 18:47:39,843 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:39.843811Z"}
2025-05-25 18:47:44,944 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:44.944444Z"}
2025-05-25 18:47:50,051 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:50.051812Z"}
2025-05-25 18:47:55,321 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:55.318947Z"}
2025-05-25 18:48:00,435 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:00.435812Z"}
2025-05-25 18:48:05,537 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:05.537077Z"}
2025-05-25 18:48:10,628 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:10.628524Z"}
2025-05-25 18:48:15,726 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:15.726263Z"}
2025-05-25 18:48:20,812 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:20.812546Z"}
2025-05-25 18:48:25,913 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:25.913146Z"}
2025-05-25 18:48:31,005 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:31.005795Z"}
2025-05-25 18:48:36,241 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:36.241753Z"}
2025-05-25 18:48:41,354 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:41.354621Z"}
2025-05-25 18:48:46,440 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:46.440156Z"}
2025-05-25 18:48:51,543 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:51.543398Z"}
2025-05-25 18:48:56,648 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:56.648632Z"}
2025-05-25 18:49:01,748 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:01.748897Z"}
2025-05-25 18:49:06,835 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:06.835114Z"}
2025-05-25 18:49:11,953 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:11.953167Z"}
2025-05-25 18:49:17,043 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:17.042975Z"}
2025-05-25 18:49:22,131 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:22.131905Z"}
2025-05-25 18:49:27,247 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:27.247879Z"}
2025-05-25 18:49:32,365 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:32.365722Z"}
2025-05-25 18:49:37,450 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:37.450813Z"}
2025-05-25 18:49:42,556 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:42.556507Z"}
2025-05-25 18:49:47,733 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:47.732157Z"}
2025-05-25 18:49:52,827 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:52.826994Z"}
2025-05-25 18:56:25,813 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.813040Z"}
2025-05-25 18:56:25,829 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.829854Z"}
2025-05-25 18:56:25,983 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:25.983491Z"}
2025-05-25 18:56:26,702 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:56:26.701900Z"}
2025-05-25 18:56:26,711 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.711457Z"}
2025-05-25 18:56:26,814 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.814161Z"}
2025-05-25 18:56:26,822 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:56:26.822377Z"}
2025-05-25 18:56:26,829 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.829242Z"}
2025-05-25 18:56:26,851 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.850483Z"}
2025-05-25 18:56:27,211 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.211016Z"}
2025-05-25 18:56:27,227 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.227622Z"}
2025-05-25 18:56:27,272 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.271913Z"}
2025-05-25 18:56:27,279 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.279753Z"}
2025-05-25 18:56:27,700 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:56:27.700315Z"}
2025-05-25 18:56:27,705 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:56:27.705214Z"}
2025-05-25 18:56:27,709 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:56:27.709864Z"}
2025-05-25 18:56:27,723 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.723839Z"}
2025-05-25 18:56:27,730 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.730528Z"}
2025-05-25 18:56:27,747 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.747738Z"}
2025-05-25 18:56:27,761 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.761294Z"}
2025-05-25 18:56:28,070 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:28.070259Z"}
2025-05-25 18:56:33,238 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:33.237884Z"}
2025-05-25 18:56:38,316 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:38.316707Z"}
2025-05-25 18:56:43,402 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:43.402283Z"}
2025-05-25 18:56:48,498 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:48.498285Z"}
2025-05-25 18:56:53,590 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:53.590497Z"}
2025-05-25 18:56:58,683 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:58.683726Z"}
2025-05-25 18:57:03,760 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:03.760435Z"}
2025-05-25 18:57:08,847 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:08.847174Z"}
2025-05-25 18:57:13,934 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:13.934447Z"}
2025-05-25 18:57:19,050 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:19.050515Z"}
2025-05-25 19:01:30,242 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.242244Z"}
2025-05-25 19:01:30,256 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.256800Z"}
2025-05-25 19:01:30,260 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.260026Z"}
2025-05-25 19:01:30,540 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T23:01:30.540050Z"}
2025-05-25 19:01:30,544 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.544876Z"}
2025-05-25 19:01:30,588 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.587751Z"}
2025-05-25 19:01:30,624 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T23:01:30.624308Z"}
2025-05-25 19:01:30,631 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.631470Z"}
2025-05-25 19:01:30,687 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.687613Z"}
2025-05-25 19:01:30,745 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.744985Z"}
2025-05-25 19:01:30,776 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.776673Z"}
2025-05-25 19:01:30,809 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.809217Z"}
2025-05-25 19:01:30,837 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.837682Z"}
2025-05-25 19:01:31,032 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T23:01:31.031999Z"}
2025-05-25 19:01:31,056 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T23:01:31.056726Z"}
2025-05-25 19:01:31,100 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.100850Z"}
2025-05-25 19:01:31,112 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.111346Z"}
2025-05-25 19:01:31,125 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.125518Z"}
2025-05-25 19:01:31,135 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.135681Z"}
2025-05-25 19:01:31,140 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.140644Z"}
2025-05-25 19:01:31,264 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.263406Z"}
2025-05-25 19:01:31,473 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.472968Z"}
2025-05-25 19:01:31,498 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.498262Z"}
2025-05-25 19:01:34,205 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DnP1YHAe... sold 0.2772 SOL of PLS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:34.205803Z"}
2025-05-25 19:01:38,710 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FSxwKvXC... sold 1.9802 SOL of nilsuisou", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:38.709945Z"}
2025-05-25 19:01:41,576 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of BT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:41.576205Z"}
2025-05-25 19:01:43,996 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3KJWMTwq... sold 2.9703 SOL of biocompute", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:43.996669Z"}
2025-05-25 19:01:47,979 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of GOATSEUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:47.979850Z"}
2025-05-25 19:01:48,265 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9h3CegiR... sold 2.2300 SOL of BIOCOMPUTE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:48.264022Z"}
2025-05-25 19:01:49,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BX5pa8eP... sold 2.9703 SOL of bc", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:49.024906Z"}
2025-05-25 19:01:51,404 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.4752 SOL of truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:51.404226Z"}
2025-05-25 19:01:57,862 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of BIOPC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:57.862308Z"}
2025-05-25 19:01:58,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F7BtcvEr... sold 2.4752 SOL of SYBAU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:58.025858Z"}
2025-05-25 19:02:03,091 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: animeQsS... sold 2.9703 SOL of biocomp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:03.091694Z"}
2025-05-25 19:02:04,182 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FR4HKPjq... sold 0.9901 SOL of investment", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.182290Z"}
2025-05-25 19:02:04,999 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2zt4HPCD... sold 4.9505 SOL of VEO3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.998929Z"}
2025-05-25 19:02:08,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: qbrKg1oq... sold 2.8000 SOL of FreeWill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:08.241891Z"}
2025-05-25 19:02:09,558 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 28z37CM9... sold 0.1188 SOL of Alien", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:09.552782Z"}
2025-05-25 19:02:12,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cztdSB5... sold 1.7822 SOL of Brucify ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:12.430532Z"}
2025-05-25 19:02:14,726 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of BULL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.725950Z"}
2025-05-25 19:02:14,957 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H1p7CyPF... sold 4.0000 SOL of Truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.957827Z"}
2025-05-25 19:02:15,224 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5heqWPem... sold 0.0010 SOL of GBIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:15.224122Z"}
2025-05-25 19:02:17,366 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6NiaaKRg... sold 1.4851 SOL of coinbase", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:17.366775Z"}
2025-05-25 19:02:19,378 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of shell", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:19.373117Z"}
2025-05-25 19:02:22,131 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of bt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:22.130403Z"}
2025-05-25 19:02:23,218 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4pMQrW7f... sold 0.9901 SOL of chopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:23.218559Z"}
2025-05-25 19:02:31,518 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CLVNpy5e... sold 0.0990 SOL of ENZO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:31.518577Z"}
2025-05-25 19:02:39,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4fC9hcfS... sold 0.9000 SOL of modl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:39.019897Z"}
2025-05-25 19:02:44,411 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H7JGZzpK... sold 0.9901 SOL of ndnp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:44.410935Z"}
2025-05-25 19:02:47,808 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of MEGA Coin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:47.808211Z"}
