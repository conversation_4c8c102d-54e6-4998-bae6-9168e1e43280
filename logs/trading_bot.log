2025-05-25 17:54:55,737 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:55.736854Z"}
2025-05-25 17:54:56,397 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:54:56.397454Z"}
2025-05-25 17:54:56,403 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.403735Z"}
2025-05-25 17:54:56,413 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.412323Z"}
2025-05-25 17:54:56,447 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:54:56.447070Z"}
2025-05-25 17:54:56,487 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:54:56.487287Z"}
2025-05-25 17:54:56,498 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:54:56.498359Z"}
2025-05-25 17:54:56,516 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:54:56.516584Z"}
2025-05-25 17:54:56,528 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:54:56.528819Z"}
2025-05-25 17:54:56,537 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.537572Z"}
2025-05-25 17:54:56,543 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:54:56.543847Z"}
2025-05-25 17:54:56,550 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.550749Z"}
2025-05-25 17:54:56,560 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.560229Z"}
2025-05-25 17:54:56,566 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:54:56.566354Z"}
2025-05-25 17:54:56,573 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.572971Z"}
2025-05-25 17:56:10,941 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:10.941571Z"}
2025-05-25 17:56:11,118 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:56:11.118560Z"}
2025-05-25 17:56:11,124 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.124063Z"}
2025-05-25 17:56:11,129 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.129341Z"}
2025-05-25 17:56:11,135 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:56:11.135574Z"}
2025-05-25 17:56:11,150 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:56:11.150751Z"}
2025-05-25 17:56:11,191 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:56:11.191162Z"}
2025-05-25 17:56:11,219 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:56:11.219027Z"}
2025-05-25 17:56:11,235 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:56:11.235235Z"}
2025-05-25 17:56:11,248 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.248662Z"}
2025-05-25 17:56:11,276 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:56:11.276372Z"}
2025-05-25 17:56:11,289 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.289307Z"}
2025-05-25 17:56:11,301 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.301738Z"}
2025-05-25 17:56:11,311 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:56:11.311540Z"}
2025-05-25 17:56:11,315 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.315073Z"}
2025-05-25 17:57:35,285 - test_bot - INFO - {"event": "\u23f0 Test started at: 2025-05-25 17:57:35", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.285544Z"}
2025-05-25 17:57:35,294 - test_bot - INFO - {"event": "\ud83d\ude80 Starting Minimal Solana Trading Bot Test...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.293997Z"}
2025-05-25 17:57:35,298 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.298890Z"}
2025-05-25 17:57:35,318 - test_bot - INFO - {"event": "\ud83d\udcca Testing database connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.318141Z"}
2025-05-25 17:57:35,423 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:57:35.423852Z"}
2025-05-25 17:57:35,426 - test_bot - INFO - {"event": "\u2705 Database initialized successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.426162Z"}
2025-05-25 17:57:35,428 - test_bot - INFO - {"event": "\u2699\ufe0f Testing configuration...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.428409Z"}
2025-05-25 17:57:35,439 - test_bot - INFO - {"event": "RPC URL: https://api.mainnet-beta.solana.com", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.439508Z"}
2025-05-25 17:57:35,441 - test_bot - INFO - {"event": "Pump.fun monitoring: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.441886Z"}
2025-05-25 17:57:35,444 - test_bot - INFO - {"event": "Copy trading: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.444079Z"}
2025-05-25 17:57:35,476 - test_bot - INFO - {"event": "Telegram bot: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.476631Z"}
2025-05-25 17:57:35,479 - test_bot - INFO - {"event": "\u2705 Configuration loaded successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.478979Z"}
2025-05-25 17:57:35,489 - test_bot - INFO - {"event": "\ud83d\ude80 Testing Pump.fun API connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.489327Z"}
2025-05-25 17:57:35,493 - test_bot - ERROR - {"event": "\u274c Pump.fun API test failed: 'PumpFunMonitor' object has no attribute 'initialize'", "logger": "test_bot", "level": "error", "timestamp": "2025-05-25T21:57:35.493535Z"}
2025-05-25 17:57:35,510 - test_bot - INFO - {"event": "\u23f1\ufe0f Running monitoring test for 30 seconds...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.509941Z"}
2025-05-25 17:57:35,519 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:57:35.519199Z"}
2025-05-25 17:57:35,670 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:35.669959Z"}
2025-05-25 17:57:40,754 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:40.754005Z"}
2025-05-25 17:57:45,838 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:45.838399Z"}
2025-05-25 17:57:50,923 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:50.923363Z"}
2025-05-25 17:57:56,042 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:56.042403Z"}
2025-05-25 17:58:01,133 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:58:01.133000Z"}
2025-05-25 17:58:05,521 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.521289Z"}
2025-05-25 17:58:05,524 - test_bot - INFO - {"event": "\u2705 Monitoring test completed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.523943Z"}
2025-05-25 17:58:05,526 - test_bot - INFO - {"event": "\ud83e\uddf9 Cleaning up...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.526420Z"}
2025-05-25 17:58:05,541 - test_bot - INFO - {"event": "\u2705 Database connections closed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.541837Z"}
2025-05-25 17:58:05,555 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.555592Z"}
2025-05-25 17:58:05,557 - test_bot - INFO - {"event": "\ud83c\udf89 All core functionality tests passed!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.557839Z"}
2025-05-25 17:58:05,568 - test_bot - INFO - {"event": "\u2705 The Solana Trading Bot core is working correctly", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.563329Z"}
2025-05-25 17:58:05,577 - test_bot - INFO - {"event": "\n\ud83d\ude80 CORE FUNCTIONALITY TEST: PASSED", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.577430Z"}
2025-05-25 17:58:05,591 - test_bot - INFO - {"event": "The bot's core systems are working correctly!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.591562Z"}
2025-05-25 17:58:05,623 - test_bot - INFO - {"event": "You can now run the full bot with: python run_bot.py", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.623162Z"}
2025-05-25 17:58:05,625 - test_bot - INFO - {"event": "\u23f0 Test completed at: 2025-05-25 17:58:05", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.625613Z"}
2025-05-25 17:58:05,640 - PumpFunMonitor - INFO - {"event": "New token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.640499Z"}
2025-05-25 17:58:05,652 - PumpFunMonitor - INFO - {"event": "Trending token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.652611Z"}
2025-05-25 17:58:05,658 - PumpFunMonitor - INFO - {"event": "Cleanup task cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.658466Z"}
2025-05-25 18:46:31,456 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.456573Z"}
2025-05-25 18:46:31,622 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:46:31.622346Z"}
2025-05-25 18:46:31,625 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.625427Z"}
2025-05-25 18:46:31,627 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.627806Z"}
2025-05-25 18:46:31,630 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:46:31.630520Z"}
2025-05-25 18:46:31,655 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T22:46:31.655722Z"}
2025-05-25 18:46:31,663 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T22:46:31.663001Z"}
2025-05-25 18:46:31,742 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T22:46:31.741884Z"}
2025-05-25 18:46:31,774 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T22:46:31.763601Z"}
2025-05-25 18:46:31,781 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.781163Z"}
2025-05-25 18:46:31,820 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T22:46:31.820381Z"}
2025-05-25 18:46:31,869 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.869355Z"}
2025-05-25 18:46:31,894 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.894049Z"}
2025-05-25 18:46:31,905 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T22:46:31.905015Z"}
2025-05-25 18:46:31,935 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.935541Z"}
2025-05-25 18:47:38,973 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.973327Z"}
2025-05-25 18:47:38,993 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.993781Z"}
2025-05-25 18:47:39,023 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.023435Z"}
2025-05-25 18:47:39,145 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:47:39.145619Z"}
2025-05-25 18:47:39,158 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.158521Z"}
2025-05-25 18:47:39,163 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.163394Z"}
2025-05-25 18:47:39,177 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:47:39.177044Z"}
2025-05-25 18:47:39,209 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.209601Z"}
2025-05-25 18:47:39,228 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.228144Z"}
2025-05-25 18:47:39,288 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.288079Z"}
2025-05-25 18:47:39,296 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.296179Z"}
2025-05-25 18:47:39,326 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.326022Z"}
2025-05-25 18:47:39,391 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.380255Z"}
2025-05-25 18:47:39,613 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:47:39.613500Z"}
2025-05-25 18:47:39,623 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:47:39.623665Z"}
2025-05-25 18:47:39,628 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:47:39.628676Z"}
2025-05-25 18:47:39,639 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.639707Z"}
2025-05-25 18:47:39,647 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.646932Z"}
2025-05-25 18:47:39,658 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.658237Z"}
2025-05-25 18:47:39,665 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.665154Z"}
2025-05-25 18:47:39,843 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:39.843811Z"}
2025-05-25 18:47:44,944 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:44.944444Z"}
2025-05-25 18:47:50,051 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:50.051812Z"}
2025-05-25 18:47:55,321 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:55.318947Z"}
2025-05-25 18:48:00,435 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:00.435812Z"}
2025-05-25 18:48:05,537 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:05.537077Z"}
2025-05-25 18:48:10,628 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:10.628524Z"}
2025-05-25 18:48:15,726 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:15.726263Z"}
2025-05-25 18:48:20,812 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:20.812546Z"}
2025-05-25 18:48:25,913 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:25.913146Z"}
2025-05-25 18:48:31,005 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:31.005795Z"}
2025-05-25 18:48:36,241 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:36.241753Z"}
2025-05-25 18:48:41,354 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:41.354621Z"}
2025-05-25 18:48:46,440 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:46.440156Z"}
2025-05-25 18:48:51,543 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:51.543398Z"}
2025-05-25 18:48:56,648 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:56.648632Z"}
2025-05-25 18:49:01,748 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:01.748897Z"}
2025-05-25 18:49:06,835 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:06.835114Z"}
2025-05-25 18:49:11,953 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:11.953167Z"}
2025-05-25 18:49:17,043 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:17.042975Z"}
2025-05-25 18:49:22,131 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:22.131905Z"}
2025-05-25 18:49:27,247 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:27.247879Z"}
2025-05-25 18:49:32,365 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:32.365722Z"}
2025-05-25 18:49:37,450 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:37.450813Z"}
2025-05-25 18:49:42,556 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:42.556507Z"}
2025-05-25 18:49:47,733 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:47.732157Z"}
2025-05-25 18:49:52,827 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:52.826994Z"}
