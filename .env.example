# Solana Trading Bot Environment Variables

# Solana Configuration
SOLANA_PRIVATE_KEY=your_base58_encoded_private_key_here
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WS_URL=wss://api.mainnet-beta.solana.com

# Database Configuration
DATABASE_URL=sqlite:///solana_trading_bot.db

# API Keys (Optional)
HELIUS_API_KEY=your_helius_api_key_here
QUICKNODE_API_KEY=your_quicknode_api_key_here
BIRDEYE_API_KEY=your_birdeye_api_key_here

# Trading Configuration
MAX_DAILY_LOSS_SOL=5.0
MAX_POSITION_SIZE_SOL=1.0
BASE_POSITION_SIZE_SOL=0.1

# Risk Management
STOP_LOSS_PERCENTAGE=0.15
TAKE_PROFIT_PERCENTAGE=2.0
SLIPPAGE_TOLERANCE=0.05

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_AUTHORIZED_USERS=123456789,987654321  # Comma-separated user IDs

# Feature Flags
ENABLE_COPY_TRADING=true
ENABLE_WALLET_DISCOVERY=true
ENABLE_PERFORMANCE_TRACKING=true
ENABLE_RISK_MANAGEMENT=true
ENABLE_TELEGRAM_BOT=true
ENABLE_NOTIFICATIONS=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log
