# Solana Trading Bot

A comprehensive Python bot that monitors Solana wallets and memecoins in real-time to identify and track profitable traders (snipers, insiders, successful devs, whales) and automatically copy their trades with advanced risk management.

## Features

### 🎯 **Trader Identification & Classification**

- **Snipers**: Fast entries on new tokens with high success rates
- **Insiders**: Early entries before major pumps with unusual timing
- **Successful Devs**: Wallets associated with successful token launches
- **Whales**: Large position sizes with market-moving trades

### 📊 **Performance Tracking**

- Real-time wallet performance analysis
- ROI calculation and tracking (10x, 20x, 100x+ gains)
- Win rate and trading pattern analysis
- Historical performance metrics

### 🤖 **Copy Trading Engine**

- Automatic trade execution when tracked wallets trade
- Intelligent position sizing based on confidence scores
- Real-time trade replication with minimal delay

### 🛡️ **Advanced Risk Management**

- Stop loss and take profit automation
- Daily loss limits and position size controls
- Emergency stop mechanisms
- Real-time risk monitoring

### 🔍 **Real-time Monitoring**

- WebSocket connection to Solana blockchain
- DEX transaction monitoring (Raydium, Orca, Jupiter, **Pump.fun**)
- **Pump.fun API integration** for new token launches
- **Snipe detection** within seconds of token creation
- Token balance change detection
- Instant trade notifications

## Installation

1. **Clone the repository**

```bash
git clone <repository-url>
cd solana_trading_bot
```

2. **Install dependencies**

```bash
pip install -r requirements.txt
```

3. **Set up environment variables**

```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Initialize the database**

```bash
python -c "import asyncio; from src.data.database import db_manager; asyncio.run(db_manager.initialize())"
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required
SOLANA_PRIVATE_KEY=your_base58_encoded_private_key_here
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Optional
HELIUS_API_KEY=your_helius_api_key_here
BIRDEYE_API_KEY=your_birdeye_api_key_here
```

### Configuration File

Edit `config.yaml` to customize:

- **Trading parameters**: Position sizes, slippage tolerance
- **Risk management**: Stop loss, take profit, daily limits
- **Trader classification**: ROI thresholds, confidence scores
- **Monitoring settings**: Update intervals, DEX programs

## Usage

### Start the Bot

```bash
# Full bot with all features
python run_bot.py

# Or just pump.fun scanner
python pump_scanner.py
```

### Monitor Performance

The bot logs all activities and provides real-time status updates:

```
2024-01-01 12:00:00 - INFO - Solana Trading Bot started successfully
2024-01-01 12:00:01 - INFO - Transaction monitoring started
2024-01-01 12:00:02 - INFO - Classified trader ABC123... as sniper with confidence 0.85
2024-01-01 12:00:03 - INFO - Copy trade executed: DEF456... -> 0.5 SOL
```

### API Integration

The bot integrates with:

- **Pump.fun API**: For real-time token launches and trading data
- **Jupiter API**: For DEX aggregation and swaps
- **Birdeye API**: For token metadata and prices
- **Solana RPC**: For blockchain data and transaction execution

## Architecture

```
src/
├── config/          # Configuration management
├── blockchain/      # Solana client and transaction monitoring
├── wallet_tracker/  # Wallet analysis and trader classification
├── trading/         # Copy trading and risk management
├── data/           # Database models and operations
├── utils/          # Logging and helper functions
└── main.py         # Main bot orchestration
```

### Key Components

1. **Transaction Monitor**: Real-time Solana transaction monitoring
2. **Wallet Analyzer**: Performance analysis and profit calculation
3. **Trader Classifier**: AI-based trader type identification
4. **Copy Trader**: Automated trade execution engine
5. **Risk Manager**: Advanced risk management system
6. **Order Executor**: DEX integration and trade execution

## Trader Classification Algorithm

### Sniper Detection

- **Speed Score**: Time between token creation and first buy
- **ROI Performance**: Minimum 5x returns
- **Hold Time**: Typically under 24 hours
- **Success Rate**: High win rate on fast entries

### Insider Detection

- **Early Entry Score**: Buying before major price movements
- **Exceptional ROI**: Minimum 20x returns
- **Timing Patterns**: Unusual entry timing before pumps
- **Information Advantage**: Consistent early positioning

### Dev Detection

- **Token Creation Correlation**: Association with successful launches
- **Exceptional Returns**: Minimum 50x ROI
- **Launch Patterns**: Consistent success across multiple tokens
- **Wallet Behavior**: Pre-launch accumulation patterns

### Whale Detection

- **Position Size**: Minimum 10 SOL positions
- **Market Impact**: Trades that move token prices
- **Volume Patterns**: Large, consistent trading volumes
- **Liquidity Provision**: Often involved in providing liquidity

## Risk Management

### Position Sizing

- **Base Size**: Configurable base position (default: 0.1 SOL)
- **Confidence Multiplier**: 0.5x to 2x based on trader confidence
- **Maximum Size**: Hard limit per position (default: 1 SOL)
- **Balance Protection**: Never risk more than 90% of available balance

### Stop Loss & Take Profit

- **Stop Loss**: Automatic sell at 15% loss (configurable)
- **Take Profit**: Automatic sell at 200% gain (configurable)
- **Trailing Stops**: Optional trailing stop loss
- **Emergency Exit**: Force close all positions if needed

### Daily Limits

- **Maximum Daily Loss**: Hard stop at daily loss limit
- **Position Limits**: Maximum concurrent positions
- **Emergency Stop**: Automatic halt on consecutive losses

## Performance Metrics

The bot tracks comprehensive performance metrics:

- **Total Trades**: Number of copy trades executed
- **Win Rate**: Percentage of profitable trades
- **Total P&L**: Cumulative profit/loss in SOL
- **Average ROI**: Mean return on investment
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Largest peak-to-trough decline

## Security Considerations

- **Private Key Management**: Store private keys securely
- **API Rate Limits**: Respect API rate limits
- **Error Handling**: Comprehensive error handling and recovery
- **Logging**: Detailed logging for audit trails
- **Backup**: Regular database backups

## Troubleshooting

### Common Issues

1. **Connection Errors**

   - Check RPC URL and network connectivity
   - Verify API keys and rate limits

2. **Transaction Failures**

   - Ensure sufficient SOL balance for fees
   - Check slippage tolerance settings

3. **Classification Issues**
   - Verify minimum trade thresholds
   - Check wallet activity requirements

### Logs

Check logs in `logs/trading_bot.log` for detailed error information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Disclaimer

This bot is for educational purposes only. Trading cryptocurrencies involves significant risk. Always:

- Test thoroughly on devnet before mainnet
- Start with small position sizes
- Monitor performance closely
- Understand the risks involved
- Never invest more than you can afford to lose

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Review the logs for error details
