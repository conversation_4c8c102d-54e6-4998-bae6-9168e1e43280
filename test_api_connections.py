#!/usr/bin/env python3
"""
Test script to verify all API connections for the Solana Trading Bot.
Tests Jupiter, Birdeye, DexScreener, Pump.fun, and Helius APIs.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, Optional
from datetime import datetime

# Test configuration
TEST_CONFIG = {
    # Jupiter API (New endpoints)
    "jupiter": {
        "quote_url": "https://lite-api.jup.ag/swap/v1/quote",
        "price_url": "https://lite-api.jup.ag/price/v2",
        "tokens_url": "https://lite-api.jup.ag/tokens/v1/mints/tradable",
    },
    
    # Birdeye API
    "birdeye": {
        "base_url": "https://public-api.birdeye.so",
        "price_url": "https://public-api.birdeye.so/defi/price",
        "trending_url": "https://public-api.birdeye.so/defi/token_trending",
        "new_listings_url": "https://public-api.birdeye.so/defi/v2/tokens/new_listing",
    },
    
    # DexScreener API
    "dexscreener": {
        "base_url": "https://api.dexscreener.com",
        "search_url": "https://api.dexscreener.com/latest/dex/search",
        "pairs_url": "https://api.dexscreener.com/latest/dex/pairs/solana",
    },
    
    # Pump.fun API (V3)
    "pumpfun": {
        "base_url": "https://frontend-api-v3.pump.fun",
        "coins_latest_url": "https://frontend-api-v3.pump.fun/coins/latest",
        "coins_live_url": "https://frontend-api-v3.pump.fun/coins/currently-live",
        "sol_price_url": "https://frontend-api-v3.pump.fun/sol-price",
    },
    
    # Helius RPC
    "helius": {
        "mainnet_url": "https://mainnet.helius-rpc.com",
        "fallback_url": "https://api.mainnet-beta.solana.com",
    }
}

# Test tokens
TEST_TOKENS = {
    "SOL": "So11111111111111111111111111111111111111112",
    "USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "BONK": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
}

class APITester:
    def __init__(self):
        self.session = None
        self.results = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={"User-Agent": "SolanaBot-APITest/1.0"}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, name: str, url: str, params: Dict = None, headers: Dict = None) -> Dict[str, Any]:
        """Test a single API endpoint."""
        start_time = time.time()
        
        try:
            async with self.session.get(url, params=params, headers=headers) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    try:
                        data = await response.json()
                        return {
                            "status": "✅ SUCCESS",
                            "response_time": f"{response_time:.2f}s",
                            "status_code": response.status,
                            "data_keys": list(data.keys()) if isinstance(data, dict) else "array",
                            "data_size": len(str(data)),
                        }
                    except json.JSONDecodeError:
                        text = await response.text()
                        return {
                            "status": "⚠️ SUCCESS (Non-JSON)",
                            "response_time": f"{response_time:.2f}s",
                            "status_code": response.status,
                            "content_type": response.headers.get("content-type", "unknown"),
                            "data_size": len(text),
                        }
                else:
                    error_text = await response.text()
                    return {
                        "status": "❌ FAILED",
                        "response_time": f"{response_time:.2f}s",
                        "status_code": response.status,
                        "error": error_text[:200] + "..." if len(error_text) > 200 else error_text,
                    }
                    
        except asyncio.TimeoutError:
            return {
                "status": "⏰ TIMEOUT",
                "response_time": f"{time.time() - start_time:.2f}s",
                "error": "Request timed out after 30 seconds",
            }
        except Exception as e:
            return {
                "status": "💥 ERROR",
                "response_time": f"{time.time() - start_time:.2f}s",
                "error": str(e),
            }
    
    async def test_jupiter_api(self):
        """Test Jupiter API endpoints."""
        print("\n🚀 Testing Jupiter API...")
        
        # Test quote endpoint
        quote_params = {
            "inputMint": TEST_TOKENS["SOL"],
            "outputMint": TEST_TOKENS["USDC"],
            "amount": "100000000",  # 0.1 SOL
            "slippageBps": "50"
        }
        
        tests = [
            ("Quote API", TEST_CONFIG["jupiter"]["quote_url"], quote_params),
            ("Price API", TEST_CONFIG["jupiter"]["price_url"], {"ids": TEST_TOKENS["SOL"]}),
            ("Tokens API", TEST_CONFIG["jupiter"]["tokens_url"], None),
        ]
        
        for test_name, url, params in tests:
            result = await self.test_endpoint(f"Jupiter {test_name}", url, params)
            self.results[f"jupiter_{test_name.lower().replace(' ', '_')}"] = result
            print(f"  {test_name}: {result['status']} ({result['response_time']})")
    
    async def test_birdeye_api(self):
        """Test Birdeye API endpoints."""
        print("\n🐦 Testing Birdeye API...")
        
        tests = [
            ("Price API", TEST_CONFIG["birdeye"]["price_url"], {"address": TEST_TOKENS["SOL"], "chain": "solana"}),
            ("Trending API", TEST_CONFIG["birdeye"]["trending_url"], {"chain": "solana", "sort_by": "volume24hUSD", "sort_type": "desc", "offset": "0", "limit": "10"}),
            ("New Listings", TEST_CONFIG["birdeye"]["new_listings_url"], {"chain": "solana", "limit": "10"}),
        ]
        
        for test_name, url, params in tests:
            result = await self.test_endpoint(f"Birdeye {test_name}", url, params)
            self.results[f"birdeye_{test_name.lower().replace(' ', '_')}"] = result
            print(f"  {test_name}: {result['status']} ({result['response_time']})")
    
    async def test_dexscreener_api(self):
        """Test DexScreener API endpoints."""
        print("\n📊 Testing DexScreener API...")
        
        tests = [
            ("Search API", TEST_CONFIG["dexscreener"]["search_url"], {"q": "BONK"}),
            ("Pairs API", f"{TEST_CONFIG['dexscreener']['pairs_url']}/{TEST_TOKENS['BONK']}", None),
        ]
        
        for test_name, url, params in tests:
            result = await self.test_endpoint(f"DexScreener {test_name}", url, params)
            self.results[f"dexscreener_{test_name.lower().replace(' ', '_')}"] = result
            print(f"  {test_name}: {result['status']} ({result['response_time']})")
    
    async def test_pumpfun_api(self):
        """Test Pump.fun API endpoints."""
        print("\n🚀 Testing Pump.fun API...")
        
        tests = [
            ("Latest Coins", TEST_CONFIG["pumpfun"]["coins_latest_url"], None),
            ("Live Coins", TEST_CONFIG["pumpfun"]["coins_live_url"], None),
            ("SOL Price", TEST_CONFIG["pumpfun"]["sol_price_url"], None),
        ]
        
        for test_name, url, params in tests:
            result = await self.test_endpoint(f"Pump.fun {test_name}", url, params)
            self.results[f"pumpfun_{test_name.lower().replace(' ', '_')}"] = result
            print(f"  {test_name}: {result['status']} ({result['response_time']})")
    
    async def test_helius_rpc(self):
        """Test Helius RPC endpoints."""
        print("\n⚡ Testing Helius RPC...")
        
        # Test RPC health endpoint
        rpc_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getHealth"
        }
        
        tests = [
            ("Helius RPC", TEST_CONFIG["helius"]["mainnet_url"]),
            ("Fallback RPC", TEST_CONFIG["helius"]["fallback_url"]),
        ]
        
        for test_name, url in tests:
            try:
                async with self.session.post(url, json=rpc_payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        status = "✅ SUCCESS" if "result" in data else "⚠️ PARTIAL"
                    else:
                        status = "❌ FAILED"
                    
                    result = {
                        "status": status,
                        "status_code": response.status,
                        "response_time": "< 1s"
                    }
                    
            except Exception as e:
                result = {
                    "status": "💥 ERROR",
                    "error": str(e),
                    "response_time": "N/A"
                }
            
            self.results[f"helius_{test_name.lower().replace(' ', '_')}"] = result
            print(f"  {test_name}: {result['status']}")
    
    async def run_all_tests(self):
        """Run all API tests."""
        print("🔍 Starting API Connection Tests...")
        print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        await self.test_jupiter_api()
        await self.test_birdeye_api()
        await self.test_dexscreener_api()
        await self.test_pumpfun_api()
        await self.test_helius_rpc()
        
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "="*60)
        print("📋 API CONNECTION TEST SUMMARY")
        print("="*60)
        
        success_count = 0
        total_count = len(self.results)
        
        for test_name, result in self.results.items():
            status = result["status"]
            if "SUCCESS" in status:
                success_count += 1
            
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\n📊 Overall Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("🎉 All API connections are working perfectly!")
        elif success_count > total_count * 0.8:
            print("✅ Most API connections are working well!")
        else:
            print("⚠️ Some API connections need attention!")
        
        print(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

async def main():
    """Main test function."""
    async with APITester() as tester:
        await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
