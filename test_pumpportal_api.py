#!/usr/bin/env python3
"""
Test the new PumpPortal WebSocket API for pump.fun monitoring.
"""

import asyncio
import json
import websockets
import aiohttp
from datetime import datetime

# New API endpoint from config
PUMPPORTAL_WS_URL = "wss://pumpportal.fun/api/data"

async def test_pumpportal_websocket():
    """Test the PumpPortal WebSocket API."""
    print("🔌 Testing PumpPortal WebSocket API...")
    print(f"URL: {PUMPPORTAL_WS_URL}")
    print("="*60)
    
    try:
        # Test WebSocket connection
        print("📡 Attempting to connect to WebSocket...")
        
        async with websockets.connect(
            PUMPPORTAL_WS_URL,
            ping_interval=20,
            ping_timeout=10,
            close_timeout=10
        ) as websocket:
            print("✅ WebSocket connection established!")
            
            # Send subscription message (common pattern for pump.fun APIs)
            subscription_messages = [
                # Subscribe to new token launches
                {"method": "subscribe", "params": ["newTokens"]},
                {"method": "subscribe", "params": ["trades"]},
                {"method": "subscribe", "params": ["tokens"]},
                {"action": "subscribe", "types": ["create", "trade"]},
                {"subscribe": "tokens"},
                {"subscribe": "trades"},
            ]
            
            # Try different subscription formats
            for i, sub_msg in enumerate(subscription_messages, 1):
                try:
                    print(f"\n📤 Sending subscription message {i}: {sub_msg}")
                    await websocket.send(json.dumps(sub_msg))
                    
                    # Wait for response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        print(f"📥 Response {i}: {response[:200]}...")
                        
                        # Try to parse as JSON
                        try:
                            data = json.loads(response)
                            print(f"✅ Valid JSON response with keys: {list(data.keys()) if isinstance(data, dict) else 'array'}")
                        except json.JSONDecodeError:
                            print("⚠️ Non-JSON response received")
                            
                    except asyncio.TimeoutError:
                        print(f"⏰ No response to subscription message {i}")
                        
                except Exception as e:
                    print(f"❌ Error with subscription {i}: {e}")
            
            # Listen for incoming messages for 30 seconds
            print(f"\n👂 Listening for messages for 30 seconds...")
            start_time = asyncio.get_event_loop().time()
            message_count = 0
            
            while asyncio.get_event_loop().time() - start_time < 30:
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    message_count += 1
                    
                    print(f"📨 Message {message_count}: {message[:150]}...")
                    
                    # Try to parse and analyze the message
                    try:
                        data = json.loads(message)
                        if isinstance(data, dict):
                            print(f"   📊 JSON keys: {list(data.keys())}")
                            
                            # Look for token-related data
                            if any(key in data for key in ['token', 'mint', 'symbol', 'name']):
                                print(f"   🪙 Token data detected!")
                            if any(key in data for key in ['trade', 'buy', 'sell', 'amount']):
                                print(f"   💰 Trade data detected!")
                                
                    except json.JSONDecodeError:
                        print(f"   ⚠️ Non-JSON message")
                        
                    # Stop after 10 messages to avoid spam
                    if message_count >= 10:
                        print(f"📊 Received {message_count} messages, stopping...")
                        break
                        
                except asyncio.TimeoutError:
                    print("⏰ No messages received in 5 seconds...")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("🔌 WebSocket connection closed")
                    break
                    
            print(f"\n📊 Total messages received: {message_count}")
            
            if message_count > 0:
                print("✅ PumpPortal WebSocket is FUNCTIONAL and sending data!")
                return True
            else:
                print("⚠️ PumpPortal WebSocket connected but no data received")
                return False
                
    except websockets.exceptions.InvalidURI:
        print("❌ Invalid WebSocket URI")
        return False
    except websockets.exceptions.ConnectionClosed:
        print("❌ WebSocket connection closed unexpectedly")
        return False
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        return False

async def test_pumpportal_http():
    """Test if PumpPortal has any HTTP endpoints."""
    print("\n🌐 Testing PumpPortal HTTP endpoints...")
    
    base_urls = [
        "https://pumpportal.fun",
        "https://pumpportal.fun/api",
        "https://pumpportal.fun/api/tokens",
        "https://pumpportal.fun/api/trades",
        "https://pumpportal.fun/api/data",
    ]
    
    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=10),
        headers={"User-Agent": "SolanaBot/1.0"}
    ) as session:
        
        for url in base_urls:
            try:
                print(f"\n🔍 Testing: {url}")
                async with session.get(url) as response:
                    print(f"   Status: {response.status}")
                    
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        print(f"   Content-Type: {content_type}")
                        
                        if 'json' in content_type:
                            try:
                                data = await response.json()
                                print(f"   ✅ JSON response with keys: {list(data.keys()) if isinstance(data, dict) else f'array[{len(data)}]'}")
                            except:
                                print(f"   ⚠️ Invalid JSON")
                        else:
                            text = await response.text()
                            print(f"   📄 Text response: {text[:100]}...")
                            
                    elif response.status == 404:
                        print(f"   ❌ Not found")
                    else:
                        print(f"   ⚠️ Status {response.status}")
                        
            except Exception as e:
                print(f"   💥 Error: {e}")

async def test_alternative_pumpfun_apis():
    """Test alternative pump.fun APIs that might work."""
    print("\n🔄 Testing Alternative Pump.fun APIs...")
    
    alternative_apis = [
        ("PumpPortal REST", "https://pumpportal.fun/api/live-trades"),
        ("PumpPortal Tokens", "https://pumpportal.fun/api/tokens/latest"),
        ("Pump.fun Direct", "https://pump.fun/api/coins"),
        ("Pump.fun Board", "https://pump.fun/board"),
    ]
    
    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=10),
        headers={"User-Agent": "SolanaBot/1.0"}
    ) as session:
        
        for name, url in alternative_apis:
            try:
                print(f"\n🧪 Testing {name}: {url}")
                async with session.get(url) as response:
                    print(f"   Status: {response.status}")
                    
                    if response.status == 200:
                        try:
                            data = await response.json()
                            if isinstance(data, list):
                                print(f"   ✅ SUCCESS - Array with {len(data)} items")
                            else:
                                print(f"   ✅ SUCCESS - Object with keys: {list(data.keys())}")
                        except:
                            text = await response.text()
                            print(f"   ✅ SUCCESS - Text: {text[:100]}...")
                    else:
                        print(f"   ❌ Failed with status {response.status}")
                        
            except Exception as e:
                print(f"   💥 Error: {e}")

async def main():
    """Main test function."""
    print("🔍 PumpPortal API Functionality Test")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # Test the WebSocket API
    ws_working = await test_pumpportal_websocket()
    
    # Test HTTP endpoints
    await test_pumpportal_http()
    
    # Test alternatives
    await test_alternative_pumpfun_apis()
    
    print("\n" + "="*60)
    print("📋 PUMPPORTAL API TEST SUMMARY")
    print("="*60)
    
    if ws_working:
        print("✅ PumpPortal WebSocket: FUNCTIONAL")
        print("🎯 RECOMMENDATION: Use the WebSocket API for real-time data")
        print("📝 UPDATE: The new API URL is working correctly!")
    else:
        print("❌ PumpPortal WebSocket: NOT FUNCTIONAL")
        print("🔄 RECOMMENDATION: Check alternative endpoints or subscription format")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return ws_working

if __name__ == "__main__":
    asyncio.run(main())
