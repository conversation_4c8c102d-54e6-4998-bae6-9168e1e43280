#!/usr/bin/env python3
"""
Test and fix Pump.fun API endpoints, specifically the live coins issue.
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_pumpfun_endpoints():
    """Test all Pump.fun API endpoints to find working ones."""
    print("🚀 Testing Pump.fun API Endpoints...")
    
    # Test different API versions and endpoints
    endpoints = [
        # V3 endpoints
        ("V3 Latest Coins", "https://frontend-api-v3.pump.fun/coins/latest"),
        ("V3 Live Coins", "https://frontend-api-v3.pump.fun/coins/currently-live"),
        ("V3 Featured 1h", "https://frontend-api-v3.pump.fun/coins/featured/1h"),
        ("V3 Featured 24h", "https://frontend-api-v3.pump.fun/coins/featured/24h"),
        ("V3 SOL Price", "https://frontend-api-v3.pump.fun/sol-price"),
        
        # V2 endpoints
        ("V2 Latest Coins", "https://frontend-api-v2.pump.fun/coins/latest"),
        ("V2 Live Coins", "https://frontend-api-v2.pump.fun/coins/currently-live"),
        ("V2 SOL Price", "https://frontend-api-v2.pump.fun/sol-price"),
        
        # V1 endpoints (original)
        ("V1 Latest Coins", "https://frontend-api.pump.fun/coins/latest"),
        ("V1 Live Coins", "https://frontend-api.pump.fun/coins/currently-live"),
        ("V1 SOL Price", "https://frontend-api.pump.fun/sol-price"),
        
        # Alternative endpoints
        ("V3 Coins (no params)", "https://frontend-api-v3.pump.fun/coins"),
        ("V2 Coins (no params)", "https://frontend-api-v2.pump.fun/coins"),
        ("V1 Coins (no params)", "https://frontend-api.pump.fun/coins"),
    ]
    
    working_endpoints = []
    failed_endpoints = []
    
    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=10),
        headers={"User-Agent": "SolanaBot/1.0"}
    ) as session:
        
        for name, url in endpoints:
            try:
                print(f"\n  Testing {name}:")
                print(f"    URL: {url}")
                
                async with session.get(url) as response:
                    print(f"    Status: {response.status}")
                    print(f"    Content-Type: {response.headers.get('content-type', 'unknown')}")
                    
                    if response.status == 200:
                        try:
                            text = await response.text()
                            data = json.loads(text)
                            
                            if isinstance(data, list):
                                print(f"    ✅ SUCCESS - Array with {len(data)} items")
                                if len(data) > 0:
                                    print(f"    Sample item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'not dict'}")
                            elif isinstance(data, dict):
                                print(f"    ✅ SUCCESS - Object with keys: {list(data.keys())}")
                            else:
                                print(f"    ✅ SUCCESS - Data type: {type(data)}")
                            
                            working_endpoints.append((name, url, data))
                            
                        except json.JSONDecodeError:
                            print(f"    ⚠️ SUCCESS (Non-JSON) - Content: {text[:100]}...")
                            working_endpoints.append((name, url, text))
                            
                    elif response.status == 404:
                        print(f"    ❌ NOT FOUND - Endpoint doesn't exist")
                        failed_endpoints.append((name, url, "404 Not Found"))
                        
                    elif response.status == 429:
                        print(f"    ⚠️ RATE LIMITED")
                        failed_endpoints.append((name, url, "Rate Limited"))
                        
                    else:
                        error_text = await response.text()
                        print(f"    ❌ FAILED - {response.status}: {error_text[:100]}...")
                        failed_endpoints.append((name, url, f"{response.status}: {error_text[:100]}"))
                        
            except asyncio.TimeoutError:
                print(f"    ⏰ TIMEOUT")
                failed_endpoints.append((name, url, "Timeout"))
                
            except Exception as e:
                print(f"    💥 ERROR: {e}")
                failed_endpoints.append((name, url, str(e)))
    
    # Print summary
    print("\n" + "="*60)
    print("📋 PUMP.FUN API TEST SUMMARY")
    print("="*60)
    
    print(f"\n✅ WORKING ENDPOINTS ({len(working_endpoints)}):")
    for name, url, data in working_endpoints:
        print(f"  • {name}: {url}")
        if isinstance(data, list) and len(data) > 0:
            print(f"    └─ Returns {len(data)} items")
        elif isinstance(data, dict):
            print(f"    └─ Returns object with {len(data)} keys")
    
    print(f"\n❌ FAILED ENDPOINTS ({len(failed_endpoints)}):")
    for name, url, error in failed_endpoints:
        print(f"  • {name}: {error}")
    
    # Return the best working endpoint for live coins
    live_endpoints = [ep for ep in working_endpoints if "live" in ep[0].lower()]
    if live_endpoints:
        best_live = live_endpoints[0]
        print(f"\n🎯 RECOMMENDED LIVE COINS ENDPOINT:")
        print(f"  {best_live[0]}: {best_live[1]}")
        return best_live[1]
    else:
        # Fallback to latest coins
        latest_endpoints = [ep for ep in working_endpoints if "latest" in ep[0].lower()]
        if latest_endpoints:
            best_latest = latest_endpoints[0]
            print(f"\n🎯 FALLBACK TO LATEST COINS ENDPOINT:")
            print(f"  {best_latest[0]}: {best_latest[1]}")
            return best_latest[1]
    
    return None

async def test_with_params():
    """Test endpoints with different parameters."""
    print("\n🔧 Testing Pump.fun with Parameters...")
    
    base_urls = [
        "https://frontend-api-v3.pump.fun/coins",
        "https://frontend-api-v2.pump.fun/coins", 
        "https://frontend-api.pump.fun/coins"
    ]
    
    param_sets = [
        {},
        {"limit": "10"},
        {"offset": "0", "limit": "10"},
        {"sort": "created_timestamp", "order": "desc", "limit": "10"},
        {"includeNsfw": "false", "limit": "10"},
    ]
    
    async with aiohttp.ClientSession() as session:
        for base_url in base_urls:
            print(f"\n  Testing {base_url}:")
            
            for i, params in enumerate(param_sets):
                try:
                    async with session.get(base_url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if isinstance(data, list):
                                print(f"    Params {i+1} ✅: {len(data)} items - {params}")
                            else:
                                print(f"    Params {i+1} ✅: object - {params}")
                        else:
                            print(f"    Params {i+1} ❌: {response.status} - {params}")
                            
                except Exception as e:
                    print(f"    Params {i+1} 💥: {e} - {params}")

async def main():
    """Main test function."""
    print("🔍 Pump.fun API Endpoint Analysis")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # Test all endpoints
    working_endpoint = await test_pumpfun_endpoints()
    
    # Test with parameters
    await test_with_params()
    
    print("\n" + "="*60)
    print("🎯 RECOMMENDATIONS:")
    
    if working_endpoint:
        print(f"✅ Use this endpoint for live/latest coins: {working_endpoint}")
    else:
        print("❌ No working endpoints found - Pump.fun API may be down")
    
    print("\n💡 CONFIGURATION UPDATE:")
    print("Update your settings.py with the working endpoint:")
    print(f'pumpfun = {{"coins_url": "{working_endpoint or "UPDATE_WITH_WORKING_ENDPOINT"}"}}')
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
